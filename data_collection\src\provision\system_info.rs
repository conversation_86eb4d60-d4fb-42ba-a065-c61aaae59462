use crate::{platform::get_bssid, provision::platform::get_system_info};
use serde::{Deserialize, Serialize};
use sysinfo::{CpuRefreshKind, Disks, MemoryRefreshKind, RefreshKind, System};

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct SystemInfo {
    kernel_version: String,
    physical_memory: u64,
    cpu_type: String,
    cpu_subtype: String,
    cpu_brand: String,
    cpu_physical_cores: usize,
    cpu_logical_cores: usize,
    hardware_model: String,
    hardware_vendor: String,
    hardware_version: String,
    hardware_serial: String,
    used_disk_space: u64,
    free_disk_space: u64,
    total_disk_space: u64,
    uptime: u64,
    free_memory: u64,
    bssid: String,
    used_memory: u64,
    platform_vendor: String,
}

impl SystemInfo {
    pub fn collect() -> Self {
        let bssid = get_bssid().unwrap_or_default();
        let system_data = get_system_info();
        let system: System = System::new_with_specifics(
            RefreshKind::nothing()
                .with_memory(MemoryRefreshKind::everything())
                .with_cpu(CpuRefreshKind::everything()),
        );
        let disk = Disks::new_with_refreshed_list();
        Self {
            kernel_version: system_data
                .get("kernel_version")
                .map_or(System::kernel_version().unwrap_or_default(), |f| {
                    f.to_owned()
                }),
            physical_memory: system_data
                .get("physical_memory")
                .map_or(system.total_memory(), |v| v.parse().unwrap()),
            cpu_type: system_data
                .get("cpu_type")
                .map_or("".to_owned(), |f| f.parse().unwrap()),
            cpu_subtype: system_data
                .get("cpu_subtype")
                .map_or("".to_owned(), |f| f.parse().unwrap()),
            cpu_brand: system_data
                .get("cpu_brand")
                .map_or(system.cpus().first().unwrap().brand().to_string(), |f| {
                    f.to_owned()
                }),
            cpu_physical_cores: system_data
                .get("cpu_physical_cores")
                .map_or(system.physical_core_count().unwrap_or_default(), |f| {
                    f.parse().unwrap()
                }),
            cpu_logical_cores: system_data
                .get("cpu_logical_cores")
                .map_or(system.cpus().len(), |f| f.parse().unwrap()),
            hardware_model: system_data
                .get("hardware_model")
                .map_or("".to_owned(), |f| f.to_owned()),
            hardware_vendor: system_data
                .get("hardware_vendor")
                .map_or("".to_owned(), |f| f.to_owned()),
            hardware_version: system_data
                .get("hardware_version")
                .map_or("".to_owned(), |f| f.to_owned()),
            hardware_serial: system_data
                .get("hardware_serial")
                .map_or("".to_owned(), |f| f.to_owned()),
            used_disk_space: disk
                .into_iter()
                .map(|item| item.total_space() - item.available_space())
                .sum(),
            free_disk_space: disk.into_iter().map(|item| item.available_space()).sum(),
            total_disk_space: disk.into_iter().map(|item| item.total_space()).sum(),
            uptime: System::uptime(),
            free_memory: system.free_memory(),
            bssid,
            used_memory: system.used_memory(),
            platform_vendor: system_data
                .get("platform_vendor")
                .map_or("".to_owned(), |f| f.to_owned()),
        }
    }
}
