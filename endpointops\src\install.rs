use crate::{args::CmdArgs, prelude::EndpointopsError, run::init_api};
use anyhow::{anyhow, Result};
use api::agent::{enroll_secret_from_id, request_enrollment};
use config::{Config, File};
use console::Style;
use crossterm::{
    cursor::MoveTo,
    event::{self, Event, KeyCode},
    execute,
    style::Print,
    terminal::{self, Clear, ClearType},
};
use database::{
    models::ServerConfig, Database, DbOptions, Model, PrimaryKey, DB_NAME, DB_NAME_SPACE,
    DB_PASSWORD, DB_USERNAME,
};
use figlet_rs::FIGfont;
use logger::{debug, error, info, ModuleLogger};
use std::{
    fs::{self},
    io::{self, Write},
    path::PathBuf,
    thread,
    time::Duration,
};
use textwrap::{wrap, Options};
use tokio::runtime::Builder;
use utils::{
    bin_config::write_config,
    constants::{MANAGER_BINARY_NAME, MANAGER_SERVICE_NAME},
    dir::get_current_dir,
    installation::copy_installation_files,
    service_manager::ServiceManager,
};

static EULA: &[u8] = include_bytes!("./EULA.txt");

pub async fn build_server_config(
    server_url: String,
    enroll_id: i64,
    uuid: Option<String>,
) -> Result<ServerConfig, EndpointopsError> {
    match ServerConfig::default()
        .set_id(PrimaryKey::ZirozenId(1))
        .set_created_at()
        .load()
        .await
    {
        Ok(result) => Ok(result),
        Err(_) => {
            let username = "zirozen".to_owned();

            let password = "Zir@zen2@24".to_owned();

            Ok(ServerConfig::new(server_url, enroll_id, uuid)
                .set_id(PrimaryKey::ZirozenId(1))
                .set_username(username)
                .set_password(password))
        }
    }
}

pub async fn verify_server_config(
    mut server_config: ServerConfig,
    args: &CmdArgs,
) -> Result<ServerConfig, EndpointopsError> {
    info!("Verifying server connectivity");
    init_api(
        server_config.url(),
        server_config.username(),
        server_config.password(),
    )
    .await?;

    let enroll_secret = enroll_secret_from_id(server_config.enroll_id()).await?;

    server_config = server_config.set_enroll_secret(enroll_secret);

    let status = request_enrollment(&server_config).await?;

    server_config = server_config.set_status(status);

    info!("Initialising database");

    match write_config(
        PathBuf::from(&args.install_path).join("config.bin"),
        &server_config,
    ) {
        Ok(bytes_written) => {
            info!("Config file has been written with bytes {}", bytes_written);
        }
        Err(error) => {
            error!(?error, "Failed to write config file");
            return Err(error.into());
        }
    };

    match Database::disposable_connection(DbOptions::new(
        PathBuf::from(&args.install_path).join("data").as_path(),
        DB_NAME_SPACE,
        DB_NAME,
        DB_USERNAME,
        DB_PASSWORD,
    ))
    .await
    {
        Ok(db) => {
            info!("Initialised database successfully.");
            if let Err(error) = server_config.persist_with_connection(db).await {
                error!(?error, "Failed to persist server config");
                return Err(error.into());
            }

            info!(
                "Server Verified with url {} and agent UUID {}",
                server_config.url(),
                server_config.uuid()
            );
        }
        Err(error) => {
            error!(?error, "Failed to initialise database");
            return Err(error.into());
        }
    };

    Ok(server_config)
}

fn display_eula() -> io::Result<()> {
    let (term_width, term_height) = terminal::size()?;

    let content = String::from_utf8_lossy(EULA);
    let lines: Vec<String> = content
        .lines()
        .map(|line| {
            if line.starts_with("[b]") {
                format!("\x1b[1m{}\x1b[0m", line.replace("[b]", ""))
            } else {
                line.to_owned()
            }
        })
        .flat_map(|line| {
            let line = line.trim();
            if line.trim().is_empty() {
                vec![String::new()]
            } else {
                wrap(line, Options::new(term_width as usize - 3))
                    .into_iter()
                    .map(|s| s.into_owned())
                    .collect()
            }
        })
        .collect();

    let height = term_height as usize - 3;
    let mut offset = 0;
    let mut stdout = io::stdout();
    terminal::enable_raw_mode()?;
    // execute!(stdout, EnterAlternateScreen)?;

    loop {
        execute!(stdout, Clear(ClearType::All), MoveTo(0, 0))?;

        for i in 0..height {
            if let Some(line) = lines.get(offset + i) {
                execute!(stdout, MoveTo(0, i as u16), Print(line))?;
            }
        }

        execute!(
            stdout,
            MoveTo(0, term_height - 1),
            Print("[↑/↓ to scroll, Enter to accept]")
        )?;
        stdout.flush()?;

        if event::poll(std::time::Duration::from_millis(500))? {
            if let Event::Key(key) = event::read()? {
                match key.code {
                    KeyCode::Up if offset > 0 => offset -= 1,
                    KeyCode::Down if offset + height < lines.len() => offset += 1,
                    KeyCode::Enter => break,
                    _ => {}
                }
            }
        }
    }

    // execute!(stdout, LeaveAlternateScreen)?;
    terminal::disable_raw_mode()?;

    Ok(())
}

fn accept_eula() -> Result<bool, EndpointopsError> {
    display_eula()?;

    // Ask for confirmation using normal stdin
    println!("\nDo you accept the EULA? (Yes/No/Default is Yes): ");
    print!("> ");
    io::stdout().flush()?;

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;

    if input.trim().to_lowercase() == "no" {
        error!("License not accepted by user");
        println!("License not accepted by user");
        Ok(false)
    } else {
        info!("License accepted by user");
        // Save acceptance flag here
        Ok(true)
    }
}

pub fn install_and_start_service(
    service_name: String,
    program: PathBuf,
    args: Vec<String>,
) -> Result<(), EndpointopsError> {
    let service = ServiceManager::new(service_name.clone(), program, None, args);

    // Get generic service by detecting what is available on the platform
    match service.install() {
        Ok(()) => {
            info!("Service {} has been installed successfully", service_name);
        }
        Err(error) => {
            error!(?error, "Failed to install service {}", service_name);
            return Err(error.into());
        }
    };
    thread::sleep(Duration::from_secs(1));
    if let Err(error) = service.force_start() {
        error!(
            ?error,
            "Force start operation failed for service {}", service_name
        )
    };
    Ok(())
}

pub fn install(args: &CmdArgs) -> Result<(), EndpointopsError> {
    let figfont = FIGfont::standard().expect("Unable to initilise fig");
    if let Some(figure) = figfont.convert("Installing EndpointOps...") {
        let style = Style::new().blue();
        println!("{}", style.apply_to(figure));
    }

    match fs::create_dir_all(&args.install_path) {
        Ok(_) => debug!("Created installation directory"),
        Err(error) => {
            error!(
                ?error,
                "Failed to create installation directory at path {}", args.install_path
            );
            return Err(anyhow!(
                "Failed to create installation directory at path {}",
                args.install_path
            )
            .into());
        }
    }

    if args.accept_eula == false {
        if accept_eula()? == false {
            return Err(EndpointopsError::LicenseError);
        }
    }

    let eula_path = PathBuf::from(&args.install_path).join("eula.txt");
    fs::write(
        eula_path,
        format!(
            "{}\n{}",
            String::from_utf8_lossy(EULA).replace("[b]", ""),
            "Accepted"
        ),
    )
    .expect("Failed to write EULA acceptance to file");

    let rt = Builder::new_multi_thread().enable_all().build();

    if let Err(error) = rt {
        error!(?error, "Failed to initialise async runtime");
        panic!("Failed to initialise async runtime");
    }

    let runtime = rt.unwrap();

    let result = runtime.block_on(async move {
        let module_loger = ModuleLogger::new("installer", None, Some("installer".to_owned()));

        let _guard = module_loger.guard();

        module_loger.set_global()?;

        info!("Logging installation");

        if args.remote_url.is_some() && args.enroll_id.is_some() {
            verify_server_config(
                build_server_config(
                    args.remote_url.as_ref().unwrap().to_owned(),
                    args.enroll_id.as_ref().unwrap().to_owned(),
                    args.uuid.to_owned(),
                )
                .await?,
                &args,
            )
            .await?;
        } else {
            let config_file_path = get_current_dir().join("install.ini");

            let config = match Config::builder()
                .add_source(File::from(config_file_path))
                .build()
            {
                Ok(config) => config,
                Err(error) => {
                    error!(?error, "Failed to read config file install.ini");
                    return Err(EndpointopsError::ConfigReadError(format!(
                        "Failed to read config with error {error:?}"
                    ))
                    .into());
                }
            };
            verify_server_config(
                build_server_config(
                    config.get("SERVER_URL")?,
                    config.get("ENROLL_ID")?,
                    args.uuid.to_owned(),
                )
                .await?,
                &args,
            )
            .await?;
        }

        copy_installation_files(
            get_current_dir().as_ref(),
            PathBuf::from(&args.install_path).as_path(),
            vec![],
        )
        .await?;

        pre_installation(args)?;

        install_and_start_service(
            MANAGER_SERVICE_NAME.to_owned(),
            PathBuf::from(&args.install_path).join(MANAGER_BINARY_NAME),
            vec!["--manager".to_string()],
        )?;

        post_installation(args);

        Ok(())
    });

    match result {
        Ok(()) => {
            if let Some(figure) = figfont.convert("Installed EndpointOps...") {
                let style = Style::new().blue();
                println!("{}", style.apply_to(figure));
            }
            Ok(())
        }
        Err(error) => Err(EndpointopsError::UnhandledError(error)),
    }
}

#[allow(unused_variables)]
fn pre_installation(args: &CmdArgs) -> Result<(), EndpointopsError> {
    #[cfg(windows)]
    {
        use utils::windows_driver_utility;

        if windows_driver_utility::has_driver() {
            return Ok(windows_driver_utility::install(&args.install_path)?);
        }
        Ok(())
    }

    #[cfg(not(windows))]
    {
        Ok(())
    }
}

#[allow(unused_variables)]
fn post_installation(args: &CmdArgs) {
    #[cfg(windows)]
    {
        use std::process::Command;
        use utils::constants::ENDPOINTOPS_BINARY_NAME;
        use utils::service_manager::ServiceStatus;
        use winreg::enums::*;
        use winreg::RegKey;

        let label = "appidsvc".to_owned();
        let app_service = ServiceManager::new(
            label,
            PathBuf::from("C:\\Windows\\system32\\svchost.exe"),
            None,
            vec![],
        );
        match app_service.status() {
            Ok(status) => {
                if status == ServiceStatus::Running {
                    info!("Appidsvc is running");
                } else {
                    info!("Appidsvc is not running");
                    if let Err(error) = app_service.start() {
                        error!(?error, "Failed to start appidsvc");
                    }
                    info!("Appidsvc started successfully");
                }
                if let Err(error) = Command::new("sc.exe")
                    .arg("config")
                    .arg("appidsvc")
                    .arg("start=auto")
                    .output()
                {
                    error!(?error, "Failed to configure appidsvc to autostart");
                } else {
                    info!("Appidsvc configured to autostart");
                }
            }
            Err(error) => {
                error!(?error, "Failed to get appidsvc status");
            }
        }

        // register uninstaller
        let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
        let path = r"Software\Microsoft\Windows\CurrentVersion\Uninstall\EndpointOps";
        match hklm.create_subkey(path) {
            Ok((key, _)) => {
                // Your app details
                let exe_path = PathBuf::from(&args.install_path)
                    .join(ENDPOINTOPS_BINARY_NAME)
                    .to_string_lossy()
                    .to_string();
                let uninstall_cmd = format!(
                    "\"{}\" --uninstall --install-path=\"{}\"",
                    exe_path, args.install_path
                );

                match key.set_value("DisplayName", &"EndpointOps") {
                    Err(error) => {
                        error!(?error, "Failed to set DisplayName key");
                    }
                    _ => {}
                };
                match key.set_value("DisplayVersion", &env!("CARGO_PKG_VERSION")) {
                    Err(error) => {
                        error!(?error, "Failed to set DisplayVersion key");
                    }
                    _ => {}
                };
                match key.set_value("Publisher", &"Zirozen Software Corporation LLP") {
                    Err(error) => {
                        error!(?error, "Failed to set Publisher key");
                    }
                    _ => {}
                };
                match key.set_value("InstallLocation", &args.install_path) {
                    Err(error) => {
                        error!(?error, "Failed to set InstallLocation key");
                    }
                    _ => {}
                };
                match key.set_value("UninstallString", &uninstall_cmd) {
                    Err(error) => {
                        error!(?error, "Failed to set UninstallString key");
                    }
                    _ => {}
                };
                match key.set_value("DisplayIcon", &exe_path) {
                    Err(error) => {
                        error!(?error, "Failed to set DisplayIcon key");
                    }
                    _ => {}
                };
            }
            Err(error) => {
                error!(?error, "Failed to open registry key {}", path);
            }
        }
    }
}
