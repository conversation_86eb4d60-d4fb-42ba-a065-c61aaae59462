pub fn force_reboot_command() -> String {
    #[cfg(windows)]
    {
        "shutdown /r /t 10".to_owned()
    }

    #[cfg(not(windows))]
    {
        "shutdown -r now".to_owned()
    }
}

pub fn warn_reboot_command() -> String {
    #[cfg(windows)]
    {
        "shutdown /r /t 60".to_owned()
    }

    #[cfg(not(windows))]
    {
        "shutdown -r +1 'System will restart in 1 minute. Please save your work.'".to_owned()
    }
}
