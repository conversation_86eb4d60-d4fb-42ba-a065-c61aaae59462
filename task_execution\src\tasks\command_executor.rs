use crate::{TaskExecutable, TaskExecutionError};
use anyhow::Result;
use database::models::{CommandType, FileAttachment};
use logger::{debug, error};
use shell::{ShellCommand, ShellOutput, ShellType};
use std::path::{Path, PathBuf};

use super::attachment_downloader::AttachmentDownloader;

#[derive(Debug)]
pub struct CommandExecutor<'a> {
    pub command_type: CommandType,
    pub command: &'a str,
    pub script: Option<PathBuf>,
    pub task: Box<&'a dyn TaskExecutable>,
    pub capture_output: bool,
    pub attachment: Option<&'a FileAttachment>,
}

impl<'a> CommandExecutor<'a> {
    pub fn new_command(command: &'a str, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self {
            command_type: CommandType::Command,
            command,
            script: None,
            task,
            attachment: None,
            capture_output: false,
        }
    }

    pub fn new_powershell(command: &'a str, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self {
            command_type: CommandType::Powershell,
            command,
            script: None,
            task,
            attachment: None,
            capture_output: false,
        }
    }

    pub fn new_script(script: PathBuf, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self {
            command_type: CommandType::Script,
            command: "",
            script: Some(script),
            task,
            attachment: None,
            capture_output: false,
        }
    }

    pub fn new_script_attachment(
        script: &'a FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            command_type: CommandType::Script,
            command: "",
            script: None,
            task,
            attachment: Some(script),
            capture_output: false,
        }
    }

    pub fn capture(mut self) -> Self {
        self.capture_output = true;

        self
    }

    pub async fn execute(self) -> Result<ShellOutput, TaskExecutionError> {
        let task_executable = self.task;
        let command = if self.command_type == CommandType::Script {
            if let Some(attachment) = self.attachment {
                AttachmentDownloader::new(attachment.clone(), task_executable.clone(), None)
                    .download()
                    .await?
                    .path_to_file_str()
            } else if let Some(script) = self.script.as_ref() {
                script.to_str().unwrap().to_owned()
            } else {
                error!("No script path is provided for command type script");
                "".to_owned()
            }
        } else {
            self.command.to_owned()
        };
        if !command.is_empty() {
            let task_dir = task_executable.get_task_dir();
            let log_file = task_executable.get_log_file();
            let logger = task_executable.get_logger();
            let command_clone = command.clone();

            match tokio::task::Builder::new()
                .name(
                    format!(
                        "command_execution {}",
                        task_executable.get_task().id.to_string()
                    )
                    .as_str(),
                )
                .spawn_blocking(move || {
                    logger.with(|| {
                        let mut cmd = ShellCommand::new(&command_clone);
                        if self.command_type == CommandType::Script {
                            cmd.use_script(Path::new(&command_clone));
                        }
                        if self.command_type == CommandType::Powershell {
                            cmd.with_type(ShellType::Powershell);
                        }

                        if !self.capture_output {
                            if log_file.as_ref().exists() {
                                cmd.with_output_file(log_file.as_ref());
                            }
                        }
                        cmd.cwd(&task_dir);

                        let output = cmd.run();

                        if let Err(error) = output {
                            error!(?error, "Failed to execute command {}", command_clone);
                            Err(TaskExecutionError::ShellExecutionError(error))
                        } else {
                            let output = output.unwrap();
                            debug!(
                                "command {} executed with exit code {}",
                                command_clone, output.exit_code
                            );
                            Ok(output)
                        }
                    })
                })
                .unwrap()
                .await
            {
                Ok(result) => match result {
                    Ok(output) => {
                        let is_success = if command.contains("wusa.exe") {
                            output.exit_code == 0
                                || output.exit_code == 3010
                                || output.exit_code == 2359302
                        } else {
                            output.succeeded()
                        };
                        task_executable
                            .write_task_log(
                                format!(
                                    "command {} executed with exit code {}",
                                    command, output.exit_code
                                ),
                                if is_success { None } else { Some("ERROR") },
                            )
                            .await;
                        Ok(output)
                    }
                    Err(error) => {
                        task_executable
                            .write_task_log(
                                format!("Failed to execute command with error {:?}", error),
                                Some("ERROR"),
                            )
                            .await;
                        Err(error)
                    }
                },
                Err(error) => {
                    error!(?error, "Failed to execute command");
                    task_executable
                        .write_task_log(
                            format!("Failed to execute command with error {:?}", error),
                            Some("ERROR"),
                        )
                        .await;
                    return Err(error.into());
                }
            }
        } else {
            error!(
                "No command found for action {:?}",
                task_executable.get_task()
            );
            task_executable
                .write_task_log(
                    format!(
                        "No command found for action {:?}",
                        task_executable.get_task()
                    ),
                    Some("ERROR"),
                )
                .await;
            Err(TaskExecutionError::NoCommandToExecute(format!(
                "No command found for executable {:?}",
                task_executable.get_task()
            )))
        }
    }
}
