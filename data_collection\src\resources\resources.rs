use super::data_models::{<PERSON>, Cpu, Disk, Memory};
use crate::{DataCollectionError, DataCollectionExtension};
use anyhow::Result;
use async_trait::async_trait;
use database::models::AgentMetadata;
use logger::{debug, error};
use serde::Serialize;
use serde_json::{json, Value};
use std::{collections::HashSet, time::Instant};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, Serial<PERSON>)]
struct GlobalDiskData {
    free: u64,
    used: u64,
    total: u64,
    utilization: f64,
}

#[derive(Debug)]
pub struct Resources<'a> {
    cpu: Cpu,
    memory: Memory,
    disks: HashSet<Disk>,
    battery: Vec<Battery>,
    disk: GlobalDiskData,
    agent_metadata: &'a AgentMetadata,
}

impl<'a> Resources<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> Self {
        Self {
            agent_metadata,
            cpu: Cpu::default(),
            disk: GlobalDiskData::default(),
            memory: Memory::default(),
            battery: vec![],
            disks: HashSet::new(),
        }
    }
}

#[async_trait]
impl<'a> DataCollectionExtension for Resources<'a> {
    fn get_refresh_interval(&self) -> u64 {
        self.agent_metadata
            .get_agent_refresh_settings()
            .resources_refresh_cycle
    }

    fn get_name(&self) -> &str {
        "resources_ext"
    }

    fn get_endpoint_id(&self) -> i64 {
        self.agent_metadata.get_endpoint_id()
    }

    fn build_payload(&self) -> Result<Value, DataCollectionError> {
        Ok(json!({
            "asset_id" : self.get_endpoint_id(),
            "data" : json!({
                "system_resources": [
                    json!({
                        "cpu": self.cpu,
                        "memory": self.memory,
                        "disk": self.disk,
                        "disks": self.disks,
                        "battery": self.battery
                    })
                ]
            })
        }))
    }

    async fn collect(&mut self) -> Result<(), DataCollectionError> {
        let logger = self.logger();
        match tokio::task::Builder::new()
            .name(self.get_name())
            .spawn_blocking(move || {
                logger.with(|| {
                    let time = Instant::now();
                    let disks = Disk::collect();
                    let batteries = Battery::collect();

                    let disk_iter =
                        disks
                            .iter()
                            .filter(|i| if cfg!(windows) { true } else { i.path == "/" });

                    let total_used_space = disk_iter.clone().map(|i| i.used).sum();
                    let total_space = disk_iter.clone().map(|i| i.total).sum();
                    let total_free_space = disk_iter.map(|i| i.free).sum();
                    let disk_utilization = ((total_used_space * 100) as f64) / (total_space as f64);

                    let global_disk = GlobalDiskData {
                        free: total_free_space,
                        used: total_used_space,
                        total: total_space,
                        utilization: f64::trunc(disk_utilization * 100.0) / 100.0,
                    };

                    let collection = (
                        Cpu::collect(),
                        Memory::collect(),
                        disks,
                        global_disk,
                        batteries,
                    );
                    debug!("Time taken for collection {:?}", time.elapsed());
                    collection
                })
            })
            .unwrap()
            .await
        {
            Ok(data) => {
                self.cpu = data.0;
                self.memory = data.1;
                self.disks = data.2;
                self.disk = data.3;
                self.battery = data.4;
                Ok(())
            }
            Err(error) => {
                error!(?error, "Failed to join task for {}", self.get_name());
                Err(error.into())
            }
        }
    }
}
