use logger::{debug, error, info};
use shell::ShellCommand;
use tokio::fs;
use utils::dir::{get_patch_cab_dir, get_patch_dir};
use windows_patch_xml_checker::WindowsXmlCheckerError;

pub struct ConfirmMissingPatchWithScript<'a> {
    uuid: &'a str,
}

impl<'a> ConfirmMissingPatchWithScript<'a> {
    pub fn new(uuid: &'a str) -> Self {
        Self { uuid }
    }

    async fn write_script(&self) -> Result<(), WindowsXmlCheckerError> {
        let patch_dir = get_patch_dir();
        let script_path = patch_dir.join("applicability_script.vbs");
        if script_path.exists() == false {
            match fs::write(
                &script_path,
                r#"
Set UpdateSession = CreateObject("Microsoft.Update.Session")
Set UpdateServiceManager = CreateObject("Microsoft.Update.ServiceManager")
Set UpdateService = UpdateServiceManager.AddScanPackageService("Offline Sync Service", Wscript.Arguments(0))
Set UpdateSearcher = UpdateSession.CreateUpdateSearcher()
WScript.Echo "Searching for updates..." & vbCRLF
UpdateSearcher.ServerSelection = 3 ' ssOthers"
UpdateSearcher.ServiceID = UpdateService.ServiceID
Set SearchResult = UpdateSearcher.Search("IsInstalled =0")
Set Updates = SearchResult.Updates
If searchResult.Updates.Count = 0 Then
WScript.Echo "There are no applicable updates."
WScript.Quit
End If
WScript.Echo "List of applicable items on the machine when using wssuscan.cab:" & vbCRLF
For I = 0 to searchResult.Updates.Count - 1
Set update = searchResult.Updates.Item(I)
WScript.Echo I +1 & " > " & update.Title
Next
WScript.Quit"#,
            ).await {
                Ok(_) => {
                    debug!("Wrote VB Applicability Script successfully");
                }
                Err(error) => {
                    error!(?error, "Failed to write VB Applicability Script");
                    return Err(error.into());
                }
            }
        } else {
            debug!("VB Applicability Script already exists using it.");
        }
        Ok(())
    }

    pub async fn verify(&self) -> Option<bool> {
        // check here using vb script if a patch is actually applicable
        info!("Checking VB Script applicability for uuid: {}", self.uuid);

        let mut is_applicable = None;

        match self.write_script().await {
            Err(_) => return is_applicable,
            _ => {}
        };

        let cab_dir = get_patch_cab_dir().join(self.uuid);
        debug!("Scanning {} directory", cab_dir.display());
        match fs::read_dir(&cab_dir).await {
            Ok(mut dir) => {
                while let Ok(file) = dir.next_entry().await {
                    match file {
                        None => {}
                        Some(file) => {
                            if file.path().extension().is_some_and(|ext| ext == "cab") {
                                let cmd = format!(
                                    "cscript {} {}",
                                    get_patch_dir()
                                        .join("applicability_script.vbs")
                                        .to_str()
                                        .unwrap(),
                                    file.path().to_str().unwrap()
                                );
                                match ShellCommand::new(&cmd).run().await {
                                    Ok(output) => {
                                        if output.succeeded() {
                                            debug!(
                                                "Script executed successfully with output {}",
                                                output.output
                                            );
                                            is_applicable = Some(true);
                                        } else {
                                            error!(
                                                "Script executed with error with output {}",
                                                output.output
                                            );
                                            is_applicable = Some(false);
                                        }
                                    }
                                    Err(error) => {
                                        error!(?error, "Failed to execute script");
                                        is_applicable = Some(false);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Err(error) => {
                error!(?error, "Failed to read directory {}", cab_dir.display());
            }
        };

        is_applicable
    }
}
