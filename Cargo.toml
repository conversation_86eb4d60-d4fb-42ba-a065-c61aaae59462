[workspace.package]
description = "An Agent to handle all EndpointOps Operations"
edition = "2021"
version = "5.0.2"
authors = ["<PERSON><PERSON> <<EMAIL>>"]

[workspace]
members = [
  "api",
  "database",
  "endpointops",
  "logger",
  "utils",
  "agent_manager",
  "shell",
  "agents",
  "windows_patch_xml_checker",
  "win_registry",
  "playground",
  "task_execution",
  "data_collection",
  "fim",
  "auth_event"
]
default-members = ["endpointops"]
resolver = "2"

# [profile.dev]
# opt-level = 1

[profile.release]
lto = true
strip = true
opt-level = 3
panic = 'abort'
codegen-units = 1


[patch.crates-io]
serde = { git = "https://github.com/frederik-uni/serde" }
serde-content = { git = "https://github.com/frederik-uni/serde-content" }
