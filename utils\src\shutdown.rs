use std::sync::{
    atomic::{AtomicBool, Ordering},
    LazyLock, Once,
};
use tokio::sync::broadcast;
use tracing::{debug, error};

static SHUTDOWN: LazyLock<broadcast::Sender<bool>> = LazyLock::new(|| {
    let (tx, _) = broadcast::channel(16);
    tx
});
static IS_RUNNING: AtomicBool = AtomicBool::new(true);
static START: Once = Once::new();

pub fn get_shutdown_signal() -> broadcast::Receiver<bool> {
    SHUTDOWN.subscribe()
}

pub fn is_system_running() -> bool {
    IS_RUNNING.load(Ordering::Relaxed)
}

pub fn trigger_shutdown() {
    if IS_RUNNING.swap(false, Ordering::SeqCst) {
        let _ = SHUTDOWN.send(true);
    }
}

pub fn start_shutdown_listener() {
    START.call_once(|| {
        tokio::spawn(async { listen_for_shutdown().await });
    });
}

async fn listen_for_shutdown() {
    match tokio::signal::ctrl_c().await {
        Ok(_) => debug!("Subscribed to ctrl+c event"),
        Err(error) => {
            error!(?error, "Failed to subscribe to ctrl+c event");
        }
    };
    trigger_shutdown();
}
