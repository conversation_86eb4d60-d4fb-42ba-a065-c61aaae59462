use crate::{execute_in_thread_pool, SystemUser};
use database::models::FileHash;
use logger::debug;
use ordered_float::OrderedFloat;
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde::Serialize;
use std::{
    collections::{HashMap, HashSet},
    path::PathBuf,
    thread,
    time::Duration,
};
use sysinfo::{
    Process as SystemProcess, ProcessRefreshKind, RefreshKind, System, MINIMUM_CPU_UPDATE_INTERVAL,
};
use tokio::runtime::Handle;

#[derive(Debug, Serialize, Default, Hash, Eq)]
pub struct Process {
    pid: u32,
    state: String,
    cpu_utilization: OrderedFloat<f64>,
    total_size: u64,
    name: String,
    cmdline: String,
    path: String,
    uid: String,
    username: String,
    ppid: u32,
    execution_time: u64,
    on_disk: String,
    sha256: String,
    md5: String,
    sha1: String,
}

struct ProcessWithUsers<'a>(&'a SystemProcess, &'a HashSet<SystemUser>);

impl From<ProcessWithUsers<'_>> for Process {
    fn from(process_with_users: ProcessWithUsers) -> Self {
        let value = process_with_users.0;
        let users = process_with_users.1;
        let file_path = value
            .exe()
            .map_or("".to_owned(), |item| item.to_string_lossy().to_string());
        let (uid, username) = value
            .user_id()
            .map_or(("".to_string(), "".to_owned()), |uid| {
                #[cfg(windows)]
                {
                    use crate::platform::get_username_from_sid;

                    let uid_str = uid
                        .to_string()
                        .split("-")
                        .last()
                        .unwrap_or_default()
                        .to_owned();
                    let user = users
                        .iter()
                        .find(|item| item.get_uid() == uid_str.parse::<u32>().unwrap());
                    (
                        uid_str,
                        user.map_or(get_username_from_sid(uid.to_string().as_str()), |item| {
                            item.get_username().to_owned()
                        }),
                    )
                }
                #[cfg(not(windows))]
                {
                    let user = users
                        .iter()
                        .find(|item| item.get_uid() == uid.to_string().parse::<u32>().unwrap());
                    (
                        uid.to_string(),
                        user.map_or("".to_owned(), |item| item.get_username().to_owned()),
                    )
                }
            });

        let name = if cfg!(target_os = "linux") {
            let p_name = value.name().to_string_lossy().to_string();
            // linux if lenght is 15 then it might be trimmed
            if p_name.len() >= 15 {
                PathBuf::from(&file_path)
                    .file_name()
                    .map_or("".to_owned(), |i| i.to_string_lossy().to_string())
            } else {
                p_name
            }
        } else {
            value.name().to_string_lossy().to_string()
        };

        Process {
            pid: value.pid().as_u32(),
            state: value.status().to_string().trim().to_owned(),
            cpu_utilization: OrderedFloat(f64::trunc(value.cpu_usage() as f64 * 100.0) / 100.0),
            total_size: value.memory(),
            name,
            on_disk: value
                .exe()
                .as_ref()
                .map(|item| if item.exists() { "yes" } else { "no" })
                .map_or("yes", |i| i)
                .to_owned(),
            cmdline: value
                .cmd()
                .iter()
                .map(|item| item.to_string_lossy().to_string())
                .collect::<Vec<String>>()
                .join(" "),
            path: file_path,
            uid,
            username,
            ppid: value.parent().map_or(0, |value| value.as_u32()),
            execution_time: value.run_time(),
            ..Default::default()
        }
    }
}

struct ProcessWithHashses<'a>(Process, &'a HashMap<String, FileHash>);

impl From<ProcessWithHashses<'_>> for Process {
    fn from(container: ProcessWithHashses) -> Self {
        let value = container.0;

        let file_hash: FileHash = if container.1.contains_key(&value.path) {
            container.1.get(&value.path).unwrap().to_owned()
        } else {
            value.path.as_str().into()
        };

        Process {
            sha256: file_hash.sha256().to_owned(),
            md5: file_hash.md5().to_owned(),
            sha1: file_hash.sha1().to_owned(),
            ..value
        }
    }
}

impl PartialEq for Process {
    fn eq(&self, other: &Self) -> bool {
        self.pid == other.pid
            && self.name == other.name
            && self.state == other.state
            && self.cmdline == other.cmdline
            && self.path == other.path
            && self.uid == other.uid
            && self.execution_time == other.execution_time
    }
}

impl Process {
    pub fn collect(sys: &mut System) -> HashSet<Self> {
        let users = SystemUser::collect();
        thread::sleep(MINIMUM_CPU_UPDATE_INTERVAL);

        sys.refresh_specifics(
            RefreshKind::nothing().with_processes(ProcessRefreshKind::everything()),
        );

        let system_processes = sys
            .processes()
            .values()
            .into_iter()
            .filter(|p| {
                if p.thread_kind().is_some() {
                    false
                } else {
                    p.cmd().len() > 0
                        || p.exe()
                            .as_ref()
                            .is_some_and(|value| value.to_string_lossy().is_empty() == false)
                }
            })
            .map(|v| ProcessWithUsers(v, &users).into())
            .collect::<HashSet<Self>>();

        let unique_exe_paths: HashSet<String> = system_processes
            .iter()
            .filter(|p| !p.path.is_empty())
            .map(|p| p.path.clone())
            .collect();

        let unique_paths: Vec<String> = unique_exe_paths
            .iter()
            .filter(|i| i.is_empty() == false)
            .map(|i| i.to_owned())
            .collect();

        let mut file_hashes: HashMap<String, FileHash> = Handle::current()
            .block_on(async move { FileHash::for_paths(unique_paths).await })
            .into_iter()
            .map(|item| (item.file_path(), item))
            .collect();

        let new_hashes = execute_in_thread_pool(|| {
            unique_exe_paths
                .into_par_iter()
                .filter(|item| file_hashes.contains_key(item) == false)
                .map(|item| {
                    thread::sleep(Duration::from_millis(250));
                    (item.clone(), item.into())
                })
                .collect::<HashMap<String, FileHash>>()
        });

        file_hashes.extend(new_hashes);

        let all_processes: HashSet<Process> = system_processes
            .into_iter()
            .map(|p| ProcessWithHashses(p, &file_hashes))
            .map(|i| i.into())
            .collect();

        debug!("Collected total {} processes", all_processes.len());

        all_processes
    }
}
