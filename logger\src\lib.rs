mod prelude;
pub use prelude::*;
use std::{
    fmt::Debug,
    fs,
    path::{Path, PathBuf},
    sync::{Arc, Mutex},
};
pub use tracing::instrument::WithSubscriber;
use tracing::Subscriber;
pub use tracing::{debug, error, info, trace, warn};
use tracing_appender::{
    non_blocking::WorkerGuard,
    rolling::{RollingFileAppender, Rotation},
};
use tracing_subscriber::{filter::FilterFn, layer::SubscriberExt, EnvFilter, Layer, Registry};
use tracing_subscriber::{
    reload::{self, Handle},
    util::SubscriberInitExt,
};
use utils::{dir::get_log_dir, GLOBAL_LOG_LEVEL};

pub(crate) static DEFAULT_MODULE_FILTER: &str =
    "{LEVEL},logger=info,endpointops={LEVEL},agent_manager={LEVEL},actors={LEVEL},database={LEVEL},shell={LEVEL},api={LEVEL},agents={LEVEL},monitoring={LEVEL},fim={LEVEL},windows_registry={LEVEL},windows_patch_xml_checker={LEVEL},{MODULE}={LEVEL},surrealdb=error,hyper=error,reqwest=error,wmi=error,globset=error,ignore=error";

pub type LogWorkerGuard = WorkerGuard;
pub type LogHandle = Handle<EnvFilter, Registry>;

#[derive(Clone)]
pub struct ModuleLogger {
    name: String,
    path: Box<Path>,
    options: Arc<Mutex<LogOptions>>,
    inner_subscriber: Arc<Box<dyn Subscriber + Send + Sync>>,
    workers: Arc<Vec<LogWorkerGuard>>,
    reload_handle: Arc<Mutex<LogHandle>>,
}

impl Debug for ModuleLogger {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ModuleLogger")
            .field("name", &self.name)
            .field("path", &self.path)
            .finish()
    }
}

impl ModuleLogger {
    pub fn new(module_name: &str, path: Option<PathBuf>, file_name: Option<String>) -> Self {
        let directory = match path {
            Some(p) => p,
            None => get_log_dir().to_path_buf(),
        };
        // let level = if level.is_some() {
        //     level.unwrap()
        // } else {
        let lock = GLOBAL_LOG_LEVEL.get().unwrap().read().unwrap();
        let level = lock.to_owned();
        drop(lock);
        let level: Level = level.into();
        // };
        trace!(
            "Initialising logger for {} with level {}",
            module_name,
            level
        );

        let file_name = if file_name.is_some() {
            file_name.unwrap()
        } else {
            module_name.to_owned()
        };

        let mut log_options = LogOptions::new(&directory, module_name.to_owned(), file_name);

        log_options.set_level(level.clone());

        let name = if module_name == "global" {
            "endpointops"
        } else {
            // set module specific logger
            log_options.set_filter(
                DEFAULT_MODULE_FILTER
                    .replace("{LEVEL}", level.into())
                    .replace("{MODULE}", module_name),
            );
            &module_name.to_lowercase()
        };

        let (subscriber, workers, reload_handle) =
            ModuleLogger::build_subscriber(&log_options).unwrap();

        Self {
            options: Arc::new(Mutex::new(log_options.clone())),
            inner_subscriber: Arc::new(subscriber),
            workers: Arc::new(workers),
            reload_handle: Arc::new(Mutex::new(reload_handle)),
            path: Box::from(directory.as_path()),
            name: name.to_owned(),
        }
    }

    pub fn get_dir(&self) -> Box<Path> {
        Box::from(self.path.as_ref().to_owned())
    }

    pub fn get_name(&self) -> &str {
        &self.name
    }

    pub fn set_log_level(&self, level: Level) {
        match self.options.try_lock() {
            Err(error) => error!(
                ?error,
                "Failed to require lock on options of logger for {}",
                self.get_name()
            ),
            Ok(mut options) => {
                options.set_level(level.clone());

                options.set_filter(
                    DEFAULT_MODULE_FILTER
                        .replace("{LEVEL}", level.clone().into())
                        .replace("{MODULE}", self.get_name()),
                );

                info!(
                    "Log options updated {:?} for logger {}",
                    options,
                    self.get_name()
                );

                let new_filter = options.filter(false);
                match self.reload_handle.try_lock() {
                    Err(error) => error!(
                        ?error,
                        "Failed to require lock on logger handle for {}",
                        self.get_name()
                    ),
                    Ok(reload_handle) => {
                        match reload_handle.modify(|filter| *filter = new_filter) {
                            Err(error) => error!(
                                ?error,
                                "Failed to set log level of logger {}",
                                self.get_name()
                            ),
                            Ok(_) => info!(
                                "successfully set log level {} for logger {}",
                                level,
                                self.get_name()
                            ),
                        }
                    }
                }
            }
        };
    }

    pub fn guard(&self) -> Arc<Vec<LogWorkerGuard>> {
        Arc::clone(&self.workers)
    }

    pub fn subscriber(&self) -> Arc<Box<dyn Subscriber + Send + Sync>> {
        Arc::clone(&self.inner_subscriber)
    }

    pub fn with<T>(&self, f: impl FnOnce() -> T) -> T {
        tracing::subscriber::with_default(self.inner_subscriber.clone(), f)
    }

    pub fn set_global(self) -> Result<(), LoggerError> {
        match tracing::subscriber::set_global_default(self.inner_subscriber.clone()) {
            Err(error) => {
                error!(?error, "Failed to set global logger");
            }
            _ => {}
        };

        Ok(())
    }

    pub fn set_default(self) -> tracing::subscriber::DefaultGuard {
        self.inner_subscriber.set_default()
    }

    fn tokio_console_filter_removal(meta: &tracing::Metadata<'_>) -> bool {
        // events will have *targets* beginning with "runtime"
        if meta.is_event() {
            return !(meta.target().starts_with("runtime")
                || meta.target().starts_with("tokio")
                || meta.target().starts_with("console_subscriber"));
        }

        // spans will have *names* beginning with "runtime". for backwards
        // compatibility with older Tokio versions, enable anything with the `tokio`
        // target as well.
        !(meta.name().starts_with("runtime.")
            || meta.target().starts_with("tokio")
            || meta.target().starts_with("console_subscriber"))
    }

    fn build_subscriber(
        options: &LogOptions,
    ) -> Result<
        (
            Box<dyn Subscriber + Send + Sync>,
            Vec<WorkerGuard>,
            Handle<EnvFilter, Registry>,
        ),
        LoggerError,
    > {
        fs::create_dir_all(options.path())?;

        let timer = LogOptions::timer();
        // initialize the application's logging
        let (reload_filter, reload_handle) = reload::Layer::new(options.filter(false));

        let mut guards = vec![];

        let file_appender = RollingFileAppender::builder()
            .max_log_files(3)
            .rotation(Rotation::DAILY) // rotate log files once per day
            .filename_prefix(options.file()) // log files will have names like "mywebservice.logging.2024-01-09"
            .filename_suffix(options.suffix()) // log files will have names like "mywebservice.logging.2024-01-09"
            .build(options.path())?;

        let (non_blocking, guard) = tracing_appender::non_blocking(file_appender);

        guards.push(guard);

        let file_layer = tracing_subscriber::fmt::layer()
            .with_ansi(false)
            .with_timer(timer.clone())
            .with_line_number(true)
            .with_thread_ids(true)
            .with_thread_names(true)
            .with_level(true)
            .with_writer(non_blocking)
            .with_filter(FilterFn::new(
                ModuleLogger::tokio_console_filter_removal
                    as for<'r, 's> fn(&'r tracing::Metadata<'s>) -> bool,
            ));

        let subscriber = Registry::default()
            .with(reload_filter)
            // .with(console_subscriber::spawn())
            .with(file_layer)
            .with(if cfg!(debug_assertions) {
                let (console_non_blocking, guard_stdout) =
                    tracing_appender::non_blocking(std::io::stdout());
                let console_layer = tracing_subscriber::fmt::layer()
                    .with_ansi(true)
                    .with_timer(timer)
                    .with_writer(console_non_blocking)
                    .with_line_number(true)
                    .with_thread_names(true)
                    .with_thread_ids(true)
                    .with_level(true)
                    .with_filter(FilterFn::new(
                        ModuleLogger::tokio_console_filter_removal
                            as for<'r, 's> fn(&'r tracing::Metadata<'s>) -> bool,
                    ));

                guards.push(guard_stdout);

                Some(console_layer)
            } else {
                None
            });

        Ok((Box::new(subscriber), guards, reload_handle))
    }
}
