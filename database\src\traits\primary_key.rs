use logger::error;
use serde::{Deserialize, Serialize};
use std::fmt::Display;
use surrealdb::sql::Thing;

#[derive(Debug, Clone, Serialize, Deserialize, Hash, PartialEq, Eq)]
#[serde(untagged)]
pub enum PrimaryKey {
    ZirozenId(i64),
    LocalId(String),
    Db(Thing),
}

impl PrimaryKey {
    pub fn is_persisted(&self) -> bool {
        match self {
            PrimaryKey::Db(_) => true,
            PrimaryKey::LocalId(_) => true,
            PrimaryKey::ZirozenId(_) => false,
        }
    }

    pub fn to_i64(&self) -> i64 {
        self.into()
    }
}

impl Default for PrimaryKey {
    fn default() -> Self {
        PrimaryKey::ZirozenId(0)
    }
}

impl Into<String> for PrimaryKey {
    fn into(self) -> String {
        match self {
            PrimaryKey::ZirozenId(id) => id.to_string(),
            PrimaryKey::LocalId(id) => id,
            PrimaryKey::Db(thing) => thing.id.to_string(),
        }
    }
}

impl Into<String> for &PrimaryKey {
    fn into(self) -> String {
        match self {
            PrimaryKey::ZirozenId(id) => id.to_string(),
            PrimaryKey::LocalId(id) => id.to_owned(),
            PrimaryKey::Db(thing) => thing.id.to_string(),
        }
    }
}

impl From<u32> for PrimaryKey {
    fn from(value: u32) -> Self {
        PrimaryKey::ZirozenId(value.into())
    }
}

impl From<u64> for PrimaryKey {
    fn from(value: u64) -> Self {
        PrimaryKey::ZirozenId(value as i64)
    }
}

impl From<&str> for PrimaryKey {
    fn from(value: &str) -> Self {
        PrimaryKey::LocalId(value.to_owned())
    }
}

impl From<String> for PrimaryKey {
    fn from(value: String) -> Self {
        PrimaryKey::LocalId(value)
    }
}

impl From<i64> for PrimaryKey {
    fn from(value: i64) -> Self {
        PrimaryKey::ZirozenId(value)
    }
}

impl From<&PrimaryKey> for i64 {
    fn from(value: &PrimaryKey) -> Self {
        match value {
            PrimaryKey::ZirozenId(id) => (*id).into(),
            PrimaryKey::LocalId(id) => {
                error!("Unable to convert local id {} to i64", id);
                0
            }
            PrimaryKey::Db(thing) => thing
                .id
                .to_raw()
                .parse()
                .expect(format!("Failed to convert {} to i64", thing.id).as_str()),
        }
    }
}

impl Display for PrimaryKey {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}",
            match self {
                PrimaryKey::ZirozenId(id) => id.to_string(),
                PrimaryKey::LocalId(id) => id.to_owned(),
                PrimaryKey::Db(thing) => thing.id.to_raw(),
            }
        )
    }
}

impl Into<i64> for PrimaryKey {
    fn into(self) -> i64 {
        match self {
            PrimaryKey::Db(thing) => thing
                .id
                .to_raw()
                .parse::<i64>()
                .expect("Failed to get u64 id"),
            PrimaryKey::ZirozenId(id) => id,
            PrimaryKey::LocalId(id) => {
                error!("Local id {} can not be converted to i64", id);
                0
            }
        }
    }
}
