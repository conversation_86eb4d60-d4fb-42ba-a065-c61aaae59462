use crate::CbsState;
use logger::{debug, error};
use serde::Deserialize;
use windows_registry::WinRegistry;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct CbsRule {
    package_identity: Option<String>,
}

impl CbsRule {
    pub fn package_installed(&self) -> bool {
        // @TODO here we don't know how to evaluate
        debug!("Inspecting CbsInstalled rule");
        true
    }

    pub fn package_installable(&self) -> bool {
        // @TODO here we don't know how to evaluate
        debug!("Inspecting CbsInstallable rule");
        true
    }

    pub fn package_installed_by_identity(&self) -> bool {
        debug!("Inspecting CbsPackageInstalledByIdentity rule");
        if let Some(identity) = self.package_identity.as_ref() {
            let base_path = format!("HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Component Based Servicing\\Packages\\{}", identity);
            match WinRegistry::new(&base_path) {
                Ok(instance) => {
                    let state: CbsState =
                        (instance.get_value::<u32>("CurrentState".to_owned()) as u8).into();
                    debug!("CBS State for identity {} is {}", identity, state);
                    state == CbsState::Installed || state == CbsState::Superseded
                }
                Err(error) => {
                    error!(?error, "Failed to read cbs for path {}", base_path);
                    false
                }
            }
        } else {
            debug!("No package identity provided so return true");
            true
        }
    }
}
