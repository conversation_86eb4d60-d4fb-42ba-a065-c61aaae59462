use agent_manager::AgentRunnable;
use anyhow::Result;
use api::data_collection::send_system_data;
use async_trait::async_trait;
use database::models::{AgentMetadata, SoftwareMeterRule, SoftwareUsage};
use database::Model;
use logger::info;
use logger::{debug, error};
use serde_json::json;
use std::collections::HashSet;
use std::ffi::OsStr;
use std::time::Duration;
use sysinfo::{Process, ProcessRefreshKind, RefreshKind, System, Users};
use tokio::select;
use tokio::time::sleep;
use utils::shutdown::get_shutdown_signal;

pub struct SoftwareMeterAgent<'a> {
    agent_metadata: &'a AgentMetadata,
}

impl<'a> SoftwareMeterAgent<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> SoftwareMeterAgent<'a> {
        SoftwareMeterAgent { agent_metadata }
    }

    fn find_root_process<'p>(
        system_data: &'p System,
        rule: &'p SoftwareMeterRule,
    ) -> Option<&'p Process> {
        let processes = system_data
            .processes_by_name(&OsStr::new(rule.file_name()))
            .collect::<Vec<&Process>>();

        let current_process_ids = processes
            .iter()
            .map(|p| p.pid().as_u32())
            .collect::<Vec<u32>>();

        let mut parent_process = processes
            .into_iter()
            .filter(|process| {
                current_process_ids.contains(&process.parent().unwrap().as_u32()) == false
            })
            .collect::<Vec<&Process>>();

        if parent_process.len() > 0 {
            Some(parent_process.remove(0))
        } else {
            None
        }
    }

    fn build_software_usage_from_process(
        rule: &SoftwareMeterRule,
        process: &Process,
        users: &Users,
    ) -> SoftwareUsage {
        SoftwareUsage {
            id: rule.id().into(),
            software_id: rule.software_id(),
            pid: process.pid().as_u32(),
            file_name: process.name().to_string_lossy().to_string(),
            start_time: process.start_time() as i64,
            elapsed_time: process.run_time() as i64,
            username: process.user_id().map_or("".to_owned(), |v| {
                users
                    .get_user_by_id(v)
                    .map_or("".to_owned(), |user| user.name().to_owned())
            }),
            uid: process.user_id().map_or("".to_owned(), |uid| {
                #[cfg(windows)]
                {
                    uid.to_string()
                        .split("-")
                        .last()
                        .unwrap_or_default()
                        .to_owned()
                }
                #[cfg(not(windows))]
                {
                    uid.to_string()
                }
            }),
            end_time: None,
        }
    }

    pub async fn process_software_meter_rules(&self) {
        let system_data = System::new_with_specifics(
            RefreshKind::nothing().with_processes(ProcessRefreshKind::everything()),
        );

        let system_users = Users::new_with_refreshed_list();

        let software_meter_rules = match SoftwareMeterRule::default().get_all(None).await {
            Ok(data) => data,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to get software meter rules from local database"
                );
                vec![]
            }
        };

        let mut software_usages = HashSet::new();

        match SoftwareUsage::default().get_all(None).await {
            Ok(records) => {
                let rule_ids = software_meter_rules
                    .iter()
                    .map(|item| item.id())
                    .collect::<Vec<i64>>();
                for record in records {
                    if rule_ids.contains(&record.id.to_i64()) == false {
                        // remove this item from db as rule is removed
                        match record.delete().await {
                            Ok(rule) => {
                                software_usages.insert(rule);
                            }
                            Err(error) => {
                                error!(
                                    ?error,
                                    "Failed to delete record form db for disabled/deleted rule"
                                );
                            }
                        };
                    }
                }
            }
            Err(error) => {
                error!(?error, "Failed to load all available software usage");
            }
        };

        for rule in software_meter_rules {
            let mut db_rule = match SoftwareUsage::default().with_id(rule.id()).load().await {
                Ok(rule) => rule,
                Err(_) => {
                    // error!(?error, "Failed to get software usage with id {}", rule.id());
                    let mut software_usage = SoftwareUsage::default();
                    software_usage.id = rule.id().into();
                    software_usage
                }
            };
            if let Some(process) = SoftwareMeterAgent::find_root_process(&system_data, &rule) {
                let usage = SoftwareMeterAgent::build_software_usage_from_process(
                    &rule,
                    process,
                    &system_users,
                );
                software_usages.insert(usage.clone());
                match usage.persist().await {
                    Ok(usage) => {
                        debug!("Updated software usage data {:?}", usage);
                    }
                    Err(error) => {
                        error!(?error, "Failed to update software usage in database");
                    }
                };
            } else {
                if db_rule.is_persisted() {
                    db_rule.id = rule.id().into();
                    db_rule.end_time = Some(chrono::Local::now().timestamp());
                    software_usages.insert(db_rule.clone());
                    match db_rule.delete().await {
                        Ok(usage) => {
                            debug!("Deleted usage as process is finished {:?}", usage);
                        }
                        Err(error) => {
                            error!(?error, "Failed to delete terminated process software usage");
                        }
                    }
                }
            }
        }

        if software_usages.len() > 0 {
            match send_system_data(json!({
                "asset_id" : self.agent_metadata.get_endpoint_id(),
                "data" : json!({
                    "software_meter_details": software_usages
                })
            }))
            .await
            {
                Ok(_) => debug!("Send software usage details successfully"),
                Err(error) => {
                    error!(?error, "Failed to send software meter details");
                }
            };
        } else {
            debug!("No relevant usage found so skipping api call");
        }
    }
}

#[async_trait]
impl AgentRunnable for SoftwareMeterAgent<'static> {
    fn get_name(&self) -> &str {
        "software_meter_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting Software Meter Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();
        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down Software Meter Agent");
                    break;
                }

                _ = sleep(Duration::from_secs(
                    self.agent_metadata.get_agent_refresh_settings().software_meter_refresh_cycle,
                )) => {
                    self.process_software_meter_rules().await;
                },
            }
        }
        info!("---------------------- Stopped Software Meter Agent ------------------------");
        Ok(())
    }
}
