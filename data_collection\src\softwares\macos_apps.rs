use super::software::{PackageType, Software};
use crate::execute_in_thread_pool;
use glob::glob;
use logger::{error, trace};
use plist::Value;
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde::Serialize;
use serde_json::json;
use std::{
    collections::HashSet,
    fs::File,
    io::BufReader,
    path::{Path, PathBuf},
};

#[derive(Debug, Serialize, Hash, PartialEq, Eq, Default)]
pub struct MacOSApp {
    name: String,
    bundle_version: String,
    bundle_id: String,
    path: String,
    bundle_executable: String,
    bundle_name: String,
    bundle_short_version: String,
    display_name: String,
    bundle_package_type: String,
    development_region: String,
    info_string: String,
    compiler: String,
    minimum_system_version: String,
    category: String,
}

impl From<MacOSApp> for Software {
    fn from(value: MacOSApp) -> Self {
        Software {
            name: value.name,
            version: value.bundle_version,
            r#type: PackageType::MacOSApplications,
            bundle_identifier: value.bundle_id,
            properties: json!({
                "bundle_package_type": value.bundle_package_type,
                "display_name": value.display_name,
                "path": value.path,
                "bundle_executable": value.bundle_executable,
                "bundle_name": value.bundle_name,
                "bundle_short_version": value.bundle_short_version,
                "development_region": value.development_region,
                "info_string": value.info_string,
                "compiler": value.compiler,
                "minimum_system_version": value.minimum_system_version,
                "category": value.category
            }),
            ..Default::default()
        }
    }
}

impl MacOSApp {
    fn build_application(plist_path: &Path) -> Option<MacOSApp> {
        trace!("Scanning path {}", plist_path.display());

        let reader = match File::open(&plist_path) {
            Ok(file) => BufReader::new(file),
            Err(error) => {
                error!(
                    ?error,
                    "Failed to parse plist file at path {}",
                    plist_path.display()
                );
                return None;
            }
        };

        let plist = match Value::from_reader(reader) {
            Ok(v) => v,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to parse plist file at path {}",
                    plist_path.display()
                );
                return None;
            }
        };

        let mut app = MacOSApp::default();
        if let Some(dict) = plist.as_dictionary() {
            if let Some(value) = dict.get("CFBundleVersion") {
                app.bundle_version = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("CFBundleIdentifier") {
                app.bundle_id = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("CFBundleExecutable") {
                app.bundle_executable = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("CFBundleName") {
                app.bundle_name = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("CFBundleShortVersionString") {
                app.bundle_short_version = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("CFBundleDisplayName") {
                app.display_name = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("CFBundlePackageType") {
                app.bundle_package_type = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("CFBundleDevelopmentRegion") {
                app.development_region = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("CFBundleGetInfoString") {
                app.info_string = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("DTCompiler") {
                app.compiler = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("LSMinimumSystemVersion") {
                app.minimum_system_version = value.as_string().unwrap_or_else(|| "").to_string();
            }
            if let Some(value) = dict.get("LSApplicationCategoryType") {
                app.category = value.as_string().unwrap_or_else(|| "").to_string();
            }
        }

        app.name = plist_path
            .parent()
            .unwrap()
            .parent()
            .unwrap()
            .file_name()
            .unwrap()
            .to_string_lossy()
            .to_string();
        app.path = plist_path
            .parent()
            .unwrap()
            .parent()
            .unwrap()
            .to_string_lossy()
            .to_string();

        Some(app)
    }

    fn generate_app_from_paths(path: &str) -> HashSet<PathBuf> {
        let glob_pattern = format!("{}/*/Contents/Info.plist", path);
        match glob(&glob_pattern) {
            Ok(paths) => paths.into_iter().filter_map(Result::ok).collect(),
            Err(error) => {
                error!(?error, "Failed to resolve glob pattern {}", glob_pattern);
                HashSet::new()
            }
        }
    }

    pub fn collect() -> HashSet<Software> {
        execute_in_thread_pool(|| {
            [
                "/System/Applications",
                "/System/Applications/Utilities",
                "/System/Library/CoreServices/Applications",
                "/Library/Apple/System/Library/CoreServices",
                "/Applications",
                "/Applications/Utilities",
                "/Users/<USER>/Applications",
                "/Users/<USER>/Applications",
                "/Users/<USER>/Desktop",
                "/Users/<USER>/Downloads",
            ]
            .into_par_iter()
            .flat_map_iter(|path| MacOSApp::generate_app_from_paths(path))
            .map(|path| MacOSApp::build_application(path.as_path()))
            .filter(|item| item.is_some())
            .map(|item| item.unwrap().into())
            .collect::<HashSet<Software>>()
        })
    }
}
