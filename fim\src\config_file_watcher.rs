use crate::{fim_event::FIMEvent, FIMConfigFile, FIMError, SystemTimedEvent, Watcher};
use api::{data_collection::send_fim_data, file::UploadedFile};
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use database::{
    models::{FIMConfig, FileHash},
    Model, Uuid,
};
use logger::{debug, error, info, trace, ModuleLogger, WithSubscriber};
use notify_debouncer_full::{
    new_debouncer, notify::RecursiveMode, DebounceEventResult, DebouncedEvent,
};
use serde_json::json;
use std::{
    collections::HashSet,
    path::{Path, PathBuf},
    sync::Arc,
    time::Duration,
};
use tokio::{
    fs::File,
    sync::{
        broadcast::Receiver,
        mpsc::{unbounded_channel, UnboundedReceiver},
        Mutex,
    },
    task::Join<PERSON><PERSON><PERSON>,
};
use tokio_stream::{wrappers::UnboundedReceiverStream, StreamExt};
use utils::{dir::get_log_dir, shutdown::is_system_running};

pub struct ConfigFileWatcher {
    logger: ModuleLogger,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
    config: Arc<FIMConfig>,
    endpoint_id: i64,
}

impl ConfigFileWatcher {
    pub fn new(config: FIMConfig, endpoint_id: i64, stop_signal_receiver: Receiver<bool>) -> Self {
        Self {
            logger: ModuleLogger::new(
                "fim",
                None,
                Some(
                    get_log_dir()
                        .join(format!("fim/{}-{}", config.id(), config.category()))
                        .to_string_lossy()
                        .to_string(),
                ),
            ),
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            config: Arc::new(config),
            endpoint_id,
        }
    }

    async fn generate_hash(path: String, logger: ModuleLogger) {
        let i_path = path.clone();
        match tokio::task::spawn_blocking(move || {
            logger.with(|| FileHash::generate_hashes(&i_path))
        })
        .await
        {
            Ok(Some(fs_hash)) => {
                let _ = fs_hash.persist().await;
                debug!("generated new hash for {} file {:?}", path, fs_hash);
            }
            Ok(None) => {
                error!("Failed to generate hashes for file {}", path);
            }
            Err(error) => {
                error!(?error, "Failed to generate hashes for file {}", path);
            }
        };
    }

    async fn should_send_file(event: &FIMEvent, logger: ModuleLogger) -> bool {
        let existing_file_hash = FileHash::for_path(event.target_path().to_owned()).await;
        debug!(
            "received existing hsash for {} file {:?}",
            event.target_path(),
            existing_file_hash
        );
        if let Some(file_hash) = existing_file_hash {
            if file_hash.md5() == event.md5()
                && file_hash.sha1() == event.sha1()
                && file_hash.sha256() == event.sha256()
            {
                false
            } else {
                ConfigFileWatcher::generate_hash(event.target_path().to_owned(), logger).await;
                true
            }
        } else {
            debug!("no existing hash for {} file", event.target_path());
            ConfigFileWatcher::generate_hash(event.target_path().to_owned(), logger).await;
            true
        }
    }

    async fn send_file(event: &FIMEvent) -> Option<UploadedFile> {
        debug!("Sending file {:?}", event.target_path());
        match api::file::upload(&Path::new(event.target_path())).await {
            Ok(file) => {
                debug!("File sent to API {:?}", file);
                Some(file)
            }
            Err(error) => {
                error!(?error, "Failed to send file to API");
                None
            }
        }
    }

    async fn sync_event(config: &FIMConfig, mut event: FIMEvent, logger: ModuleLogger) {
        if !is_system_running() {
            return;
        }
        if ConfigFileWatcher::should_send_file(&event, logger).await {
            if let Some(file) = ConfigFileWatcher::send_file(&event).await {
                event.set_fim_config_file(FIMConfigFile {
                    config_id: config.id(),
                    file_path: config
                        .paths()
                        .first()
                        .unwrap()
                        .to_string_lossy()
                        .to_string(),
                    ref_name: file.file_ref,
                    file_name: file.file_name,
                });

                // this event has been persisted and it will be sent to server later
                let _ = event.persist().await;
            }
        }
    }

    fn start_event_receiver(
        &self,
        tokio_rx: UnboundedReceiver<Vec<DebouncedEvent>>,
    ) -> JoinHandle<()> {
        let fim_config = self.config.clone();
        let logger = self.logger.clone();
        tokio::task::Builder::new()
            .name("file_event_stream")
            .spawn(
                async move {
                    let mut stream = UnboundedReceiverStream::new(tokio_rx);

                    while let Some(events) = stream.next().await.take_if(|_| is_system_running()) {
                        let events = events
                            .into_iter()
                            .take_while(|_| is_system_running())
                            .map(|event| SystemTimedEvent::new(event.event, &fim_config, &logger))
                            .map(|item| item.build_fim_event())
                            .collect::<HashSet<FIMEvent>>();

                        if let Some(event) = events.into_iter().last() {
                            ConfigFileWatcher::sync_event(&fim_config, event, logger.clone()).await;
                        }
                    }

                    info!("Event Loop exited");
                }
                .with_subscriber(self.logger.subscriber()),
            )
            .unwrap()
    }

    async fn collect_paths(&self) -> Result<PathBuf, FIMError> {
        if let Some(path) = self.config.paths().first() {
            if !path.exists() {
                return Err(FIMError::ConfigFileNotFound(
                    path.to_string_lossy().to_string(),
                ));
            }

            Ok(path.to_path_buf())
        } else {
            Err(FIMError::ConfigFileNotFound("No path found".to_string()))
        }
    }
}

#[async_trait]
impl Watcher for ConfigFileWatcher {
    async fn watch(&self) -> anyhow::Result<(), FIMError> {
        async {
            let mut shutdown_receiver = self.stop_signal_receiver.lock().await;

            let paths_to_watch = self.collect_paths().await;

            if let Err(error) = paths_to_watch {
                error!(?error, "Failed to collect path to watch");
                shutdown_receiver.recv().await.ok();
                return Err(error);
            }

            let path_to_watch = paths_to_watch.unwrap();

            let mut event = FIMEvent::default();
            event.set_target_path(path_to_watch.to_string_lossy().to_string());
            event.generate_hash();

            if ConfigFileWatcher::should_send_file(&event, self.logger.clone()).await {
                if let Some(file) = ConfigFileWatcher::send_file(&event).await {
                    let event_time = match File::open(&path_to_watch).await {
                        Ok(f) => match f.metadata().await {
                            Ok(metadata) => {
                                Into::<DateTime<Utc>>::into(metadata.created().unwrap()).timestamp()
                            }
                            Err(error) => {
                                error!(
                                    ?error,
                                    "Failed to open file metadata {}",
                                    path_to_watch.display()
                                );
                                chrono::Utc::now().timestamp()
                            }
                        },
                        Err(error) => {
                            error!(?error, "Failed to open file {}", path_to_watch.display());
                            chrono::Utc::now().timestamp()
                        }
                    };
                    // here send first time file change api
                    match send_fim_data(json!({
                        "asset_id" : self.endpoint_id,
                        "data" : {
                            "fim_config_file" : {
                                "file_name" : file.file_name,
                                "file_path" : path_to_watch.to_string_lossy().to_string(),
                                "event_id" : Uuid::new_v4().to_string(),
                                "event_time" : event_time,
                                "config_id" : self.config.id(),
                                "ref_name" : file.file_ref
                        }
                      }
                    }))
                    .await
                    {
                        Ok(_) => debug!("Sent first time file change api"),
                        Err(error) => error!(?error, "Failed to send first time file change api"),
                    };
                }
            }

            info!("Starting watcher for config {}", self.config.category());

            let (notify_tx, tokio_rx) = unbounded_channel();

            let mut stream_task = self.start_event_receiver(tokio_rx);

            let mut watcher = match new_debouncer(
                Duration::from_secs(5),
                None,
                move |res: DebounceEventResult| match res {
                    Ok(events) => {
                        match notify_tx.send(events) {
                            Ok(_) => trace!("Send Event to tokio notifier"),
                            Err(error) => {
                                error!(?error, "Failed to publish event to tokio notifier");
                            }
                        };
                    }

                    Err(e) => error!("Notify Errors: {:?}", e),
                },
            ) {
                Ok(watcher) => watcher,
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to initialise watcher for config {:?}", self.config
                    );
                    shutdown_receiver.recv().await.ok();
                    return Err(error.into());
                }
            };

            debug!("Watching path {}", path_to_watch.display());
            match watcher.watch(&path_to_watch, RecursiveMode::NonRecursive) {
                Ok(_) => {
                    debug!("Started watching path {}", path_to_watch.display());
                }
                Err(error) => {
                    error!(?error, "Failed to watch path {}", path_to_watch.display());
                }
            }

            tokio::select! {
                biased;

                _ = shutdown_receiver.recv() => {
                    info!("Stopping Watcher started for config {}", self.config.category());
                    watcher.stop();
                },

                _ = &mut stream_task => {
                    info!("Event Receiver task is finished executing");
                }
            };

            info!(
                "Shutting down file watcher for config {}",
                self.config.category()
            );

            Ok(())
        }
        .with_subscriber(self.logger.subscriber())
        .await
    }
}
