use agent_manager::AgentError;
use anyhow::Error as AnyhowError;
use api::ApiError;
use config::ConfigError;
use database::DatabaseError;
use logger::LoggerError;
use std::io;
use thiserror::Error;
use tokio::task::JoinError;
#[cfg(windows)]
use windows_service::Error as WinServiceError;

#[derive(Debug, Error)]
pub enum EndpointopsError {
    #[error("Fork Error: {0}")]
    ForkError(String),

    #[cfg(windows)]
    #[error("Driver Installation Failed: {0}")]
    DriverInstallationFailed(String),

    #[cfg(windows)]
    #[error("Driver Uninstallation Failed: {0}")]
    DriverUninstallationFailed(String),

    #[error("Config Error: {0}")]
    ConfigReadError(String),

    #[error("Config Error: {0}")]
    ConfigError(#[from] ConfigError),

    #[error("Tokio Task Join Error: {0:?}")]
    JoinError(#[from] JoinError),

    #[error("License Error")]
    LicenseError,

    #[cfg(windows)]
    #[error("Windows Service Error: {0}")]
    WinServiceError(#[from] WinServiceError),

    #[error("IO Error: {0:?}")]
    RuntimeError(#[from] io::Error),

    #[error("Database Error: {0:?}")]
    DatabaseError(#[from] DatabaseError),

    #[error("Logger Error: {0:?}")]
    LoggerError(#[from] LoggerError),

    #[error("Api Error: {0:?}")]
    ApiError(#[from] ApiError),

    #[error("Agent Manager Error: {0:?}")]
    AgentError(#[from] AgentError),

    #[error("Agent Manager Error: Failed to provision agent")]
    FailedToProvisionAgent,

    #[error("Agent Upgrade: Failed to uninstall rdp agent to apply upgrade")]
    FailedToUninstallRdpService,

    #[error("Unhandled Error: {0:?}")]
    UnhandledError(#[from] AnyhowError),
}
