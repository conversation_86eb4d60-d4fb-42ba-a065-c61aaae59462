use crate::{
    tasks::{attachment_downloader::AttachmentDownloader, unzip::Unzip},
    TaskExecutable, TaskExecutionError,
};
use anyhow::Result;
use api::patch::send_patch_discovered_data;
use database::models::FileAttachment;
use futures::stream::{FuturesOrdered, StreamExt};
use logger::{debug, error, info, ModuleLogger};
use serde_json::json;
use shell::ShellCommand;
use std::{path::Path, sync::Arc, time::Duration};
use tokio::{fs, sync::Semaphore, time::Instant};
use utils::{
    cpu::concurrency_cores,
    dir::{get_log_dir, get_patch_dir},
};

#[derive(Debug, Clone)]
pub struct PatchFile {
    sequence: u32,
    name: String,
    path: Box<Path>,
    evaluation_result: Option<bool>,
}

impl PatchFile {
    pub fn is_valid_file(&self) -> bool {
        let parts = self.get_name_parts();
        parts.len() > 0
    }

    pub fn get_uuid(&self) -> String {
        let parts = self.get_name_parts();
        let uuid = parts.first().unwrap();
        uuid.to_owned()
    }

    pub fn get_name_parts(&self) -> Vec<String> {
        let split_parts = self.name.split('.');
        split_parts.map(|i| i.to_owned()).collect()
    }
}

pub struct MacPatchFinder<'a> {
    version: &'a str,
    endpoint_id: i64,
    task: Box<&'a dyn TaskExecutable>,
    files: Vec<PatchFile>,
    semaphore: Arc<Semaphore>,
}

impl<'a> MacPatchFinder<'a> {
    pub fn new(version: &'a str, endpoint_id: i64, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self {
            version,
            files: vec![],
            endpoint_id,
            task,
            semaphore: Arc::new(Semaphore::new(concurrency_cores(30))),
        }
    }

    async fn collect_files(&self) -> Result<Vec<PatchFile>> {
        let mut files = vec![];
        let mut counter = 1;
        let dist_folder = get_patch_dir().join("dist");

        let mut dir = fs::read_dir(dist_folder).await?;

        while let Some(entry) = dir.next_entry().await? {
            if entry.path().extension().is_some_and(|ext| ext == "dist") {
                let path = entry.path();

                debug!("Collecting file {:?}", path.display());

                let file = path.file_name();

                if file.is_some() {
                    let patch_file = PatchFile {
                        sequence: counter,
                        path: Box::from(path.as_path()),
                        name: file.unwrap().to_str().unwrap().to_owned(),
                        evaluation_result: None,
                    };

                    files.push(patch_file);

                    counter = counter + 1;
                }
            } else {
                debug!("Skipping file with extension {}", entry.path().display());
            }
        }

        Ok(files)
    }

    async fn process_single_file(semaphore: Arc<Semaphore>, mut file: PatchFile) -> PatchFile {
        let file_clone = file.clone();
        match tokio::task::Builder::new()
            .name(format!("patch {}", file.get_uuid()).as_str())
            .spawn(async move {
                let _permit = semaphore.acquire().await;
                let file_clone = file.clone();
                match tokio::task::Builder::new().name(format!("xml scanning {}", file.get_uuid()).as_str())
                    .spawn_blocking(move || {
                        ModuleLogger::new(
                            "actors::patch_scan::mac",
                            Some(get_log_dir().join("patch/mac-scanning")),
                            Some(file.name.clone()),
                        ).with(|| {
                            info!("Processing file {}", file.name);
                            if file.is_valid_file() {
                                let dist_dir = get_patch_dir().join("dist");
                                let uuid = file.get_uuid();
                                let pkg_path = dist_dir.join(format!("{}.pkg", uuid));

                                let cmd = format!(
                                    "productbuild --distribution {} {}",
                                    file.path.to_str().unwrap(),
                                    pkg_path.to_str().unwrap()
                                );

                                match ShellCommand::new(&cmd)
                                .cwd(&dist_dir)
                                .run()
                                {
                                    Ok(output) => {
                                        if output.succeeded() {
                                            debug!("Built pkg file successfully with output {}", output.output);
                                        } else {
                                            error!(
                                                "Failed to build pkg file from dist file {} with output {} and exit code {}", file.name, output.output, output.exit_code
                                            );
                                        }
                                    }
                                    Err(error) => {
                                        error!(
                                            ?error,
                                            "Failed to build pkg file from dist file {}", file.name
                                        );
                                        file.evaluation_result = Some(false);
                                        return file;
                                    }
                                };

                                let check_command = format!("sudo installer -pkg {} -target /", pkg_path.to_str().unwrap());
                                match ShellCommand::new(&check_command)
                                .cwd(&dist_dir)
                                .run()
                                {
                                    Ok(output) => {
                                        if output.succeeded() {
                                            debug!(
                                                "Installer command exit code {} and output \n {}\n",
                                                output.exit_code, output.output
                                            );
                                            if output.output.contains("installing at base path")
                                                || output.output.contains("upgrading at base path")
                                            {
                                                file.evaluation_result = Some(true);
                                            } else {
                                                file.evaluation_result = Some(false);
                                            }
                                        } else {
                                            error!(
                                                "Failed to run installer command with output {} and exit code {}", output.output, output.exit_code
                                            );
                                            file.evaluation_result = Some(false);
                                        }
                                    }
                                    Err(error) => {
                                        error!(
                                            ?error,
                                            "Failed to run installer command for file {}", file.name
                                        );
                                        file.evaluation_result = Some(false);
                                    }
                                }
                            } else {
                                error!("{:?} File is not valid to scan", file);
                                file.evaluation_result = Some(false);
                            }
                            // just to cool down cpu
                            std::thread::sleep(Duration::from_millis(200));
                            file
                        })
                    })
                .unwrap().await {
                    Ok(file) => file,
                        Err(error) => {
                            error!(?error, "Faile to execute xml file {}", file_clone.get_uuid());
                            file_clone
                        }
                }
            })
            .unwrap()
            .await
        {
            Ok(file) => file,
            Err(error) => {
                error!(?error, "Failed to join xml process task");
                file_clone
            }
        }
    }

    async fn process_files(self) -> Result<Vec<String>> {
        let files = self.files;

        let total_files = files.len();

        debug!("Processing total {} Files", total_files);
        let mut missing_uuids = vec![];
        let mut files_futures = files
            .into_iter()
            .map(|file| MacPatchFinder::process_single_file(self.semaphore.clone(), file))
            .collect::<FuturesOrdered<_>>();

        while let Some(file) = files_futures.next().await {
            debug!(
                "Files [{} of {}] is finished processing with status {}",
                file.sequence,
                total_files,
                file.evaluation_result.as_ref().unwrap_or(&false)
            );
            if file.evaluation_result.is_some_and(|item| item) {
                missing_uuids.push(file.get_uuid());
                info!("File {} is valid", file.name);
            }
        }

        debug!("Got missing ids: {:?}", missing_uuids);

        Ok(missing_uuids)
    }

    pub async fn scan(mut self) -> Result<()> {
        let time = Instant::now();
        let patch_directory = get_patch_dir();

        fs::create_dir_all(&patch_directory).await?;

        let attachment = AttachmentDownloader::new(
            FileAttachment {
                real_name: format!("{}.7z", self.version),
                ref_name: format!("{}", self.version),
                zirozen_download_url: Some(format!(
                    "/patch/asset/macos/download-dist/{}",
                    self.version
                )),
                ..Default::default()
            },
            self.task.clone(),
            Some(patch_directory),
        )
        .download()
        .await?;

        info!("macOS patch file download successfully {:?}", attachment);

        match Unzip::new(&attachment, self.task.clone())
            .extract(None)
            .await
        {
            Err(error) => {
                return Err(TaskExecutionError::ExtractionError(format!(
                    "Failed to extract archive {} with error {:?}",
                    attachment.real_name, error
                ))
                .into())
            }
            Ok(_) => {}
        };

        info!("File {} extracted successfully", attachment.real_name);

        self.files = match self.collect_files().await {
            Ok(files) => files,
            Err(error) => {
                error!(?error, "Failed to collect macOS patch file entries");
                return Err(TaskExecutionError::MacOSPatchScanError(format!(
                    "Failed to collect macOS patch file entries with error {:?}",
                    error
                ))
                .into());
            }
        };

        let endpoint_id = self.endpoint_id;

        let missing_uuids = self.process_files().await?;

        send_patch_discovered_data(
            endpoint_id,
            json!({
                "macMissingPatches": missing_uuids,
            }),
        )
        .await?;

        debug!("Time taken for full scanning {:?}", time.elapsed());

        Ok(())
    }
}
