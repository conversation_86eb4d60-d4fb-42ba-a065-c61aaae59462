use agent_manager::Agent<PERSON><PERSON><PERSON><PERSON>;
use anyhow::{anyhow, Result};
use async_trait::async_trait;
use logger::{debug, error, info};
use std::sync::Arc;
use tokio::process::Command;
use tokio::select;
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::sync::Mutex;
use utils::constants::{MESH_AGENT_BINARY_NAME, MESH_AGENT_SERVICE_NAME};
use utils::dir::get_current_dir;
use utils::service_manager::{ServiceManager, ServiceStatus};

pub struct RdpAgent {
    stop_signal_sender: Sender<bool>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl RdpAgent {
    pub fn new() -> RdpAgent {
        let (stop_signal_sender, stop_signal_receiver) = channel(1);

        RdpAgent {
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            stop_signal_sender,
        }
    }

    async fn install_rdp_service(&self, service_manager: &ServiceManager) -> Result<()> {
        match service_manager.status() {
            Ok(status) => {
                if status == ServiceStatus::NotInstalled {
                    match Command::new(service_manager.get_program().to_string_lossy().to_string())
                        .arg("-install")
                        .arg(format!("--installPath={}", get_current_dir().display()))
                        .output()
                        .await
                    {
                        Ok(output) => {
                            debug!(
                                "Got service installation output as {}",
                                String::from_utf8_lossy(&output.stdout)
                            );
                            return Ok(());
                        }
                        Err(error) => {
                            error!(?error, "Failed to install remote desktop service");
                            return Err(anyhow!("Failed to install remote desktop service"));
                        }
                    };
                } else {
                    Ok(())
                }
            }
            Err(error) => {
                error!(?error, "Failed to get service status");
                return Err(anyhow!("Failed to get service status"));
            }
        }
    }

    fn start_rdp_service(&self, service_manager: &ServiceManager) -> Result<()> {
        match service_manager.status() {
            Ok(status) => {
                if status == ServiceStatus::Running {
                    return Ok(());
                } else {
                    service_manager.start()
                }
            }
            Err(error) => {
                error!(?error, "Failed to get service status");
                return Err(anyhow!("Failed to get service status"));
            }
        }
    }

    fn stop_rdp_service(&self, service_manager: &ServiceManager) -> Result<()> {
        match service_manager.status() {
            Ok(status) => {
                if status == ServiceStatus::Running {
                    service_manager.stop()
                } else {
                    return Ok(());
                }
            }
            Err(error) => {
                error!(?error, "Failed to get service status");
                return Err(anyhow!("Failed to get service status"));
            }
        }
    }
}

#[async_trait]
impl AgentRunnable for RdpAgent {
    fn get_name(&self) -> &str {
        "rdp_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting RDP Agent ------------------------");
        let receiver_arc = self.stop_signal_receiver.clone();

        let mut receiver = receiver_arc.lock().await;

        let service_label = MESH_AGENT_SERVICE_NAME;

        let exe_path = get_current_dir().join(MESH_AGENT_BINARY_NAME);

        let service_manager =
            ServiceManager::new(service_label.to_owned(), exe_path.clone(), None, vec![]);

        if let Err(error) = self.install_rdp_service(&service_manager).await {
            error!(?error, "Failed to install rdp service");
            receiver.recv().await;
            return Err(anyhow!("Failed to install rdp service"));
        }

        if let Err(error) = self.start_rdp_service(&service_manager) {
            error!(?error, "Failed to start rdp service");
            receiver.recv().await;
            return Err(anyhow!("Failed to start rdp service"));
        }

        loop {
            select! {
                biased;

                _ = receiver.recv() => {
                    info!("Shutting Down RDP Agent");
                    if let Err(error) = self.stop_rdp_service(&service_manager) {
                        error!(?error, "Failed to start rdp service");
                        receiver.recv().await;
                        return Err(anyhow!("Failed to start rdp service"));
                    }
                    break;
                }
            }
        }
        info!("---------------------- Stopped RDP Agent ------------------------");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if let Err(error) = self.stop_signal_sender.send(true).await {
            error!(?error, "Failed to send stop signal to RDP agent");
        };
        Ok(())
    }

    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
}
