[package]
name = "utils"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
serde = "1.0.219"
service-manager = "0.8.0"
anyhow = { version = "1.0.94", features = ["backtrace"] }
tracing = { version = "0.1.41", features = ["log", "std"] }
bincode = { version = "2.0.1", features = ["serde"] }
tokio = { version = "1.43.0", features = ["full"] }
serde_json = "1.0.133"
