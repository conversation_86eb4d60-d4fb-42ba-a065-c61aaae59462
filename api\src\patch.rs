use crate::{get_client, ApiError};
use serde_json::{json, Value};
use std::collections::HashMap;

pub async fn send_patch_system_scan_result(
    endpoint_id: i64,
    data: &HashMap<String, Value>,
) -> anyhow::Result<Value, ApiError> {
    get_client()?
        .post::<Value, _>(
            {
                cfg_if! {
                    if #[cfg(target_os = "linux")] {
                        format!("/patch/asset/linux/installed-patches/{}", endpoint_id).to_owned()
                    } else {
                        format!("/patch/asset/discovered-patch-data/{}", endpoint_id).to_owned()
                    }
                }
            },
            json!({
                "patchData": data
            }),
        )
        .await
}

pub async fn send_patch_discovered_data(
    endpoint_id: i64,
    data: Value,
) -> anyhow::Result<Value, ApiError> {
    get_client()?
        .post::<Value, _>(
            format!("/patch/asset/discovered-patch-data/{}", endpoint_id).to_owned(),
            data,
        )
        .await
}

#[cfg(target_os = "linux")]
pub async fn get_linux_latest_package_info(
    endpoint_id: i64,
) -> anyhow::Result<HashMap<String, String>, ApiError> {
    get_client()?
        .get::<HashMap<String, String>, _>(
            format!(
                "/patch/asset/linux/prepare/latestPackageInfo/{}",
                endpoint_id
            )
            .to_owned(),
        )
        .await
}

#[cfg(windows)]
pub async fn get_windows_xml_files(
    endpoint_id: u32,
) -> anyhow::Result<HashMap<String, String>, ApiError> {
    get_client()?
        .post::<HashMap<String, String>, _>(
            format!("/patch/asset/applicability-rule-filemap/{}", endpoint_id).to_owned(),
            json!({}),
        )
        .await
}

#[cfg(windows)]
pub async fn get_missing_uuids_with_superseeded_removed(
    ids: &Vec<String>,
) -> anyhow::Result<HashMap<String, bool>, ApiError> {
    get_client()?
        .post::<HashMap<String, bool>, _>(
            "/patch/asset/remove-superseded-patch".to_owned(),
            json!({
                "fileList": ids
            }),
        )
        .await
}

pub async fn trigger_patch_scan(asset_id: i64) -> anyhow::Result<String, ApiError> {
    get_client()?
        .post(
            format!("/patch/asset/execute-scan-patch/{}", asset_id),
            json!({}),
        )
        .await
}
