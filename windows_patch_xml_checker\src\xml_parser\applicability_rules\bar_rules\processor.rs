use crate::{
    xml_parser::applicability_rules::expression_evaluator::ExpressionEvaluator, WMIGetter,
};

use super::Operator;
use logger::{debug, error};
use serde::Deserialize;

fn is_64_bit() -> bool {
    cfg!(target_pointer_width = "64")
}

#[derive(Deserialize, Debug)]
#[serde(rename = "Win32_Processor")]
#[serde(rename_all = "PascalCase")]
struct SystemScannedValue {
    pub architecture: i16,
    pub level: i16,
    pub revision: Option<i16>,
    pub number_of_cores: i16,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct Processor {
    #[serde(rename = "@Number")]
    number: Option<i16>,
    #[serde(rename = "@Architecture")]
    architecture: Option<i16>,
    #[serde(rename = "@Comparison")]
    comparison: Option<Operator>,
    #[serde(rename = "@Level")]
    level: Option<i16>,
    #[serde(rename = "@Revision")]
    revision: Option<i16>,
}

impl Processor {
    fn match_system_values(&self, system_values: SystemScannedValue) -> bool {
        let operator = self.comparison.as_ref().unwrap_or(&Operator::EqualTo);
        if let Some(number) = self.number.as_ref() {
            let evaluator = ExpressionEvaluator::new(
                &system_values.number_of_cores,
                number,
                operator,
                "NumberOfProcessors",
            );
            if !evaluator.run() {
                return false;
            }
        }

        if let Some(arch) = self.architecture.as_ref() {
            if *arch == 0 && is_64_bit() {
                debug!("64 bit system doesn't comply with architecture 0");
                return false;
            } else if *arch == 9 && !is_64_bit() {
                debug!("non 64 bit system doesn't comply with architecture 9");
                return false;
            } else {
                let evaluator = ExpressionEvaluator::new(
                    &system_values.architecture,
                    arch,
                    operator,
                    "ProcessorArchitecture",
                );
                if !evaluator.run() {
                    return false;
                }
            }
        }

        if let Some(level) = self.level.as_ref() {
            let evaluator =
                ExpressionEvaluator::new(&system_values.level, level, operator, "ProcessorLevel");
            if !evaluator.run() {
                return false;
            }
        }

        if let Some(revision) = self.revision.as_ref() {
            if system_values.revision.is_none() {
                error!(
                    "Unable to get ProcessorRevision in WMI Query {:?}",
                    system_values
                );
                return false;
            }
            let evaluator = ExpressionEvaluator::new(
                system_values.revision.as_ref().unwrap(),
                revision,
                operator,
                "ProcessorRevision",
            );
            if !evaluator.run() {
                return false;
            }
        }
        true
    }

    pub fn evaluate(&self) -> bool {
        debug!("Inspecting Processor rule");

        match WMIGetter::new(None) {
            Ok(wmi_connection) => match wmi_connection.get::<SystemScannedValue>() {
                Some(result) => self.match_system_values(result),
                None => false,
            },
            Err(error) => {
                error!(?error, "Failed to open wmi connection to read system value");
                false
            }
        }
    }
}
