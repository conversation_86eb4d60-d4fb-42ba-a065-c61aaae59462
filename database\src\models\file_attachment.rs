use logger::error;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::path::Path;
use utils::dir::get_current_dir;

#[derive(PartialEq, Eq, Serialize, Deserialize, <PERSON><PERSON>, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub struct FileAttachment {
    #[serde(alias = "fileName")]
    pub real_name: String,
    pub ref_name: String,
    #[serde(alias = "downloadUrl")]
    pub public_url: Option<String>,
    pub local_path: Option<Box<Path>>,
    pub is_network_share: Option<bool>,
    pub zirozen_download_url: Option<String>,
    #[serde(alias = "fetchFromMSU")]
    pub patch_only_file_to_install: Option<bool>,
    pub request_method: Option<String>,
    pub request_body: Option<Value>,
}

impl FileAttachment {
    pub fn path_to_file_str(&self) -> String {
        if let Some(path) = self.local_path.as_ref() {
            path.join(&self.real_name).to_str().unwrap().to_owned()
        } else {
            error!("No local path found to build execution command");
            "".to_owned()
        }
    }

    pub fn path_to_file(&self) -> Box<Path> {
        if let Some(path) = self.local_path.as_ref() {
            Box::from(path.join(&self.real_name).as_path().to_owned())
        } else {
            error!("No local path found to build execution command");
            self.disk_local_path()
        }
    }

    pub fn disk_local_path(&self) -> Box<Path> {
        if let Some(path) = self.local_path.as_ref() {
            path.clone()
        } else {
            get_current_dir()
        }
    }

    pub fn extension(&self) -> &str {
        let ext = Path::new(&self.real_name).extension().unwrap_or_default();
        ext.to_str().unwrap_or("")
    }
}
