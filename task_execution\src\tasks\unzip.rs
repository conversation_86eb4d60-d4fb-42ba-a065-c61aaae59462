use anyhow::{anyhow, Context};
use async_zip::base::read::seek::ZipFileReader;
use database::models::FileAttachment;
use logger::{debug, error, info, WithSubscriber};
use std::path::PathBuf;
use tokio::{
    fs::{create_dir_all, File, OpenOptions},
    io::BufReader,
};
use tokio_tar::Archive as TarArchive;
use tokio_util::compat::{TokioAsyncReadCompatExt, TokioAsyncWriteCompatExt};

use crate::{TaskExecutable, TaskExecutionError};

/// Returns a relative path without reserved names, redundant separators, ".", or "..".
fn sanitize_file_path(path: &str) -> PathBuf {
    // Replaces backwards slashes
    path.replace('\\', "/")
        // Sanitizes each component
        .split('/')
        .map(sanitize_filename::sanitize)
        .collect()
}

pub struct Unzip<'a> {
    file: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Unzip<'a> {
    pub fn new(file: &'a FileAttachment, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self { file, task }
    }

    async fn open_file(&self) -> Result<File, TaskExecutionError> {
        debug!("Reading file {}", self.file.path_to_file_str());
        let file = File::open(self.file.path_to_file_str()).await;
        if let Err(error) = file {
            error!(
                ?error,
                "Failed to ready tar file {}",
                self.file.path_to_file_str()
            );
            Err(anyhow!("Failed to ready tar file {}", self.file.path_to_file_str()).into())
        } else {
            Ok(file.unwrap())
        }
    }

    async fn untar(&self, destination: Option<PathBuf>) -> Result<(), TaskExecutionError> {
        let mut archive = TarArchive::new(self.open_file().await?);
        let result = archive
            .unpack(destination.unwrap_or(self.file.disk_local_path().to_path_buf()))
            .await;
        if let Err(error) = result {
            error!(
                ?error,
                "Failed to unpack tar {}",
                self.file.path_to_file_str()
            );
            Err(anyhow!("Failed to unpack tar {}", self.file.path_to_file_str()).into())
        } else {
            Ok(())
        }
    }

    async fn unzip(&self, destination: Option<PathBuf>) -> Result<(), TaskExecutionError> {
        let archive = BufReader::new(self.open_file().await?).compat();
        let out_dir = destination.unwrap_or(self.file.disk_local_path().to_path_buf());
        let mut reader = ZipFileReader::new(archive)
            .await
            .context("Failed to read zip file")?;
        for index in 0..reader.file().entries().len() {
            let entry = reader.file().entries().get(index).unwrap();
            let path = out_dir.join(sanitize_file_path(entry.filename().as_str().unwrap()));
            // If the filename of the entry ends with '/', it is treated as a directory.
            // This is implemented by previous versions of this crate and the Python Standard Library.
            // https://docs.rs/async_zip/0.0.8/src/async_zip/read/mod.rs.html#63-65
            // https://github.com/python/cpython/blob/820ef62833bd2d84a141adedd9a05998595d6b6d/Lib/zipfile.py#L528
            let entry_is_dir = entry.dir().unwrap();

            let mut entry_reader = reader
                .reader_without_entry(index)
                .await
                .context("Failed to read ZipEntry")?;

            if entry_is_dir {
                // The directory may have been created if iteration is out of order.
                if !path.exists() {
                    create_dir_all(&path)
                        .await
                        .context("Failed to create extracted directory")?;
                }
            } else {
                // Creates parent directories. They may not exist if iteration is out of order
                // or the archive does not contain directory entries.
                let parent = path
                    .parent()
                    .context("A file entry should have parent directories")?;
                if !parent.is_dir() {
                    create_dir_all(parent)
                        .await
                        .context("Failed to create parent directories")?;
                }
                let writer = OpenOptions::new()
                    .write(true)
                    .truncate(true)
                    .create(true)
                    .open(&path)
                    .await
                    .context("Failed to create extracted file")?;
                futures_lite::io::copy(&mut entry_reader, &mut writer.compat_write())
                    .await
                    .context("Failed to copy to extracted file")?;

                // Closes the file and manipulates its metadata here if you wish to preserve its metadata from the archive.
            }
        }
        Ok(())
    }

    fn sevenz(
        file: FileAttachment,
        destination: Option<PathBuf>,
    ) -> Result<(), TaskExecutionError> {
        let destination = destination.unwrap_or(file.disk_local_path().to_path_buf());
        match sevenz_rust::decompress_file(file.path_to_file_str(), &destination) {
            Ok(()) => {
                info!(
                    "Successfully extracted file {} at path {}",
                    file.path_to_file_str(),
                    destination.display()
                );
                Ok(())
            }
            Err(e) => {
                error!(
                    "Failed to extract file {} with error {:?}",
                    file.path_to_file_str(),
                    e
                );
                Err(TaskExecutionError::ExtractionError(format!(
                    "Failed to extract file {} with error {:?}",
                    file.path_to_file_str(),
                    e
                )))
            }
        }
    }

    pub async fn extract(self, destination: Option<PathBuf>) -> Result<(), TaskExecutionError> {
        let file = self.file.to_owned();
        let logger = self.task.get_logger();
        match match self.file.extension() {
            "tar.gz" | "gz" => {
                self.untar(destination)
                    .with_subscriber(self.task.get_logger().subscriber())
                    .await
            }
            "zip" => {
                self.unzip(destination)
                    .with_subscriber(self.task.get_logger().subscriber())
                    .await
            }
            "7z" => match tokio::task::spawn_blocking(move || {
                logger.with(|| Unzip::sevenz(file, destination))
            })
            .with_subscriber(self.task.get_logger().subscriber())
            .await
            {
                Ok(result) => result,
                Err(error) => {
                    error!(?error, "Failed to extract 7z file in thread");
                    Err(error.into())
                }
            },
            _ => {
                return Err(TaskExecutionError::UnsupportedFileExtension(
                    self.file.extension().to_owned(),
                ));
            }
        } {
            Ok(result) => {
                self.task
                    .write_task_log(
                        format!("File {} is extracted successfully", self.file.real_name),
                        None,
                    )
                    .await;
                Ok(result)
            }
            Err(error) => {
                error!(?error, "Failed to extract file {}", self.file.real_name);
                self.task
                    .write_task_log(
                        format!("Failed to extract file {}", self.file.real_name),
                        Some("ERROR"),
                    )
                    .await;
                Err(error)
            }
        }
    }
}
