[package]
name = "agent_manager"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
database = { path = "../database" }
logger = { path = "../logger" }
utils = { path = "../utils" }
shell = { path = "../shell" }
anyhow = { version = "1.0.94", features = ["backtrace"] }
async-trait = "0.1.83"
thiserror = "2.0.4"
tokio = { version = "1.43.0", features = ["full", "tracing"] }
