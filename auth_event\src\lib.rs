#[macro_use]
extern crate cfg_if;

mod auth_event;
mod prelude;

pub use auth_event::*;
pub use prelude::*;

use async_trait::async_trait;
use tokio::sync::mpsc::{Receiver, Sender};

#[cfg(target_os = "linux")]
mod linux;
#[cfg(windows)]
mod windows;

#[async_trait]
pub trait AuthEventMonitor: Send {
    async fn monitor(
        &mut self,
        mut shutdown_recv: Receiver<()>,
        channel: Sender<AuthEvent>,
    ) -> Result<(), AuthEventError>;
}

pub fn get_default_monitor() -> Box<dyn AuthEventMonitor> {
    cfg_if! {
        if #[cfg(target_os = "linux")] {
            let monitor = linux::LinuxMonitor;
        } else if #[cfg(windows)] {
            let monitor = windows::WindowsMonitor;
        } else {
            compile_error!("Unsupported OS");
        }
    }

    Box::new(monitor)
}
