use super::Agent;
use logger::{error, info};
use std::time::Duration;
use tokio::time::sleep;

pub struct AgentManager {
    agents: Vec<Agent>,
}

impl AgentManager {
    pub fn new() -> Self {
        AgentManager { agents: vec![] }
    }

    pub fn add_agent(&mut self, agent: Agent) -> &AgentManager {
        self.agents.push(agent);
        self
    }

    pub async fn start(&mut self) {
        for agent in self.agents.iter_mut() {
            if agent.is_running() == false {
                info!("Agent Manager: Starting Agent {}", agent.get_name());

                agent.start();
            }
        }

        for agent in self.agents.iter_mut() {
            let result = agent.wait().await;

            if result.is_err() {
                error!(
                    error = ?result.as_ref().err().unwrap(),
                    "Agent Manager: Failed to complete agent task for {}",
                    agent.get_name()
                );
            } else {
                info!(
                    "Agent Manager: agent {} finished executing",
                    agent.get_name()
                );
            }
        }
    }

    pub async fn stop(&mut self) {
        for agent in self.agents.iter_mut() {
            let agent_name = &agent.get_name().clone();
            info!("Agent Manager: Stopping Agent {}", agent_name);
            tokio::select! {
                biased;

                result = agent.stop() => {
                    if result.is_err() {
                        error!(
                            error = ?result.err().unwrap(),
                            "Agent Manager: Failed to complete stop method of agent {}", agent_name
                        );
                    } else {
                        info!("Agent Manager: Finished Stopping agent {}", agent_name);
                    }
                },
                _ = sleep(Duration::from_secs(2)) => {
                    error!("Agent Manager: Failed to finish stop method in 2s timeout for agent {} forcefully aborting", agent_name);
                    if let Err(error) = agent.abort().await {
                        error!(?error, "Agent Manager: Failed to abort agent task forcefully {}", agent_name);
                    }
                }
            };
        }
    }
}
