use agent_manager::Agent<PERSON>unnable;
use anyhow::Result;
use api::agent::get_configuration;
use async_trait::async_trait;
use database::models::{AgentMetadata, FIMConfig, <PERSON><PERSON>heck, SoftwareMeterRule};
use database::Model;
use logger::{debug, error, info};
use std::f64::INFINITY;
use std::time::Duration;
use task_execution::BANDWIDTH_LIMITER;
use tokio::select;
use tokio::sync::broadcast::Sender;
use tokio::time::sleep;
use utils::shutdown::get_shutdown_signal;

pub struct ConfigurationAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    fim_restart_tx: Sender<bool>,
}

impl<'a> ConfigurationAgent<'a> {
    pub fn new(
        agent_metadata: &'a AgentMetadata,
        fim_restart_tx: Sender<bool>,
    ) -> ConfigurationAgent<'a> {
        ConfigurationAgent {
            agent_metadata,
            fim_restart_tx,
        }
    }

    pub async fn handle_refresh_call(&self) {
        match get_configuration(self.agent_metadata.get_endpoint_id()).await {
            Ok(response) => {
                if response.software_meter_cofigurations.is_some() {
                    match SoftwareMeterRule::default().delete_all().await {
                        Ok(_) => {
                            debug!("Software meter rules data deleted successfully");
                        }
                        Err(error) => {
                            error!(?error, "Failed to delete existing software meter rule data");
                        }
                    };
                    let software_meter_rules =
                        response.software_meter_cofigurations.as_ref().unwrap();
                    if software_meter_rules.len() > 0 {
                        // remove all fim config and insert this one
                        SoftwareMeterRule::default()
                            .bulk_insert(
                                software_meter_rules
                                    .into_iter()
                                    .map(|item| item.to_owned())
                                    .collect(),
                            )
                            .await;
                    }
                }
                if response.quick_checks.is_some() {
                    match QuickCheck::default().delete_all().await {
                        Ok(_) => {
                            debug!("Quick check data deleted successfully");
                        }
                        Err(error) => {
                            error!(?error, "Failed to delete existing quick check data");
                        }
                    }
                    let quick_checks = response.quick_checks.as_ref().unwrap();
                    if quick_checks.len() > 0 {
                        // remove all fim config and insert this one
                        QuickCheck::default()
                            .bulk_insert(
                                quick_checks
                                    .into_iter()
                                    .map(|item| item.to_owned())
                                    .collect(),
                            )
                            .await;
                    }
                }
                if response.fim_config.is_some() {
                    match FIMConfig::default().delete_all().await {
                        Ok(_) => {
                            debug!("FIM data deleted successfully");
                        }
                        Err(error) => {
                            error!(?error, "Failed to delete existing fim config data");
                        }
                    };
                    let fim_config = response.fim_config.as_ref().unwrap();
                    if fim_config.len() > 0 {
                        // remove all fim config and insert this one
                        FIMConfig::default()
                            .bulk_insert(
                                fim_config.into_iter().map(|item| item.to_owned()).collect(),
                            )
                            .await;
                    }
                    match self.fim_restart_tx.send(true) {
                        Ok(_) => info!("Sent FIM watch agent restart message"),
                        Err(_) => {
                            error!("Failed to send FIM watch agent restart message");
                        }
                    }
                    // agent_manager
                }
                self.agent_metadata.update_config(response);

                let bandwidth_limit = self
                    .agent_metadata
                    .get_agent_refresh_settings()
                    .max_file_download_speed
                    .map_or(INFINITY, |s| if s > 0 { s as f64 } else { INFINITY });

                debug!("Updating bandwidth limit with value {:?}", bandwidth_limit);

                BANDWIDTH_LIMITER.set_speed_limit(bandwidth_limit);
            }
            Err(error) => {
                error!(?error, "Failed to get agent configuration");
            }
        }
    }
}

#[async_trait]
impl AgentRunnable for ConfigurationAgent<'static> {
    fn get_name(&self) -> &str {
        "configuration_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting Configuration Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();
        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down Configuration Agent");
                    break;
                }

                _ = sleep(Duration::from_secs(
                    self.agent_metadata
                        .get_agent_refresh_settings()
                        .refresh_cycle,
                )) => {
                    self.handle_refresh_call().await;
                }

            }
        }
        info!("---------------------- Stopped Configuration Agent ------------------------");
        Ok(())
    }
}
