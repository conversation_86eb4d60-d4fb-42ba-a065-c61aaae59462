use crate::<PERSON>IMAgent;
use agent_manager::{<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use anyhow::Result;
use api::agent::get_configuration;
use async_trait::async_trait;
use database::models::{AgentMetadata, FIMConfig, <PERSON><PERSON><PERSON>ck, SoftwareMeterRule};
use database::Model;
use logger::{debug, error, info};
use std::f64::INFINITY;
use std::sync::Arc;
use std::time::Duration;
use task_execution::BANDWIDTH_LIMITER;
use tokio::select;
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::sync::Mutex;
use tokio::time::sleep;

pub struct ConfigurationAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    stop_signal_sender: Sender<bool>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl<'a> ConfigurationAgent<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> ConfigurationAgent<'a> {
        let (stop_signal_sender, stop_signal_receiver) = channel(1);

        ConfigurationAgent {
            agent_metadata,
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            stop_signal_sender,
        }
    }

    pub async fn handle_refresh_call(&self, fim_restart_tx: Option<&Sender<bool>>) {
        match get_configuration(self.agent_metadata.get_endpoint_id()).await {
            Ok(response) => {
                if response.software_meter_cofigurations.is_some() {
                    match SoftwareMeterRule::default().delete_all().await {
                        Ok(_) => {
                            debug!("Software meter rules data deleted successfully");
                        }
                        Err(error) => {
                            error!(?error, "Failed to delete existing software meter rule data");
                        }
                    };
                    let software_meter_rules =
                        response.software_meter_cofigurations.as_ref().unwrap();
                    if software_meter_rules.len() > 0 {
                        // remove all fim config and insert this one
                        SoftwareMeterRule::default()
                            .bulk_insert(
                                software_meter_rules
                                    .into_iter()
                                    .map(|item| item.to_owned())
                                    .collect(),
                            )
                            .await;
                    }
                }
                if response.quick_checks.is_some() {
                    match QuickCheck::default().delete_all().await {
                        Ok(_) => {
                            debug!("Quick check data deleted successfully");
                        }
                        Err(error) => {
                            error!(?error, "Failed to delete existing quick check data");
                        }
                    }
                    let quick_checks = response.quick_checks.as_ref().unwrap();
                    if quick_checks.len() > 0 {
                        // remove all fim config and insert this one
                        QuickCheck::default()
                            .bulk_insert(
                                quick_checks
                                    .into_iter()
                                    .map(|item| item.to_owned())
                                    .collect(),
                            )
                            .await;
                    }
                }
                if response.fim_config.is_some() {
                    match FIMConfig::default().delete_all().await {
                        Ok(_) => {
                            debug!("FIM data deleted successfully");
                        }
                        Err(error) => {
                            error!(?error, "Failed to delete existing fim config data");
                        }
                    };
                    let fim_config = response.fim_config.as_ref().unwrap();
                    if fim_config.len() > 0 {
                        // remove all fim config and insert this one
                        FIMConfig::default()
                            .bulk_insert(
                                fim_config.into_iter().map(|item| item.to_owned()).collect(),
                            )
                            .await;
                    }
                    if let Some(fim_restart_tx) = fim_restart_tx {
                        match fim_restart_tx.send(true).await {
                            Ok(_) => info!("Sent FIM watch agent restart message"),
                            Err(_) => {
                                error!("Failed to send FIM watch agent restart message");
                            }
                        }
                    }
                    // agent_manager
                }
                self.agent_metadata.update_config(response);

                let bandwidth_limit = self
                    .agent_metadata
                    .get_agent_refresh_settings()
                    .max_file_download_speed
                    .map_or(INFINITY, |s| if s > 0 { s as f64 } else { INFINITY });

                debug!("Updating bandwidth limit with value {:?}", bandwidth_limit);

                BANDWIDTH_LIMITER.set_speed_limit(bandwidth_limit);
            }
            Err(error) => {
                error!(?error, "Failed to get agent configuration");
            }
        }
    }
}

#[async_trait]
impl AgentRunnable for ConfigurationAgent<'static> {
    fn get_name(&self) -> &str {
        "configuration_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting Configuration Agent ------------------------");
        let mut shutdown_receiver = self.stop_signal_receiver.lock().await;

        let (fim_watch_restart_tx, fim_watch_restart_rx) = channel::<bool>(1);

        let mut fim_agent_manager = AgentManager::new();

        fim_agent_manager.add_agent(Agent::new(Box::new(FIMAgent::new(
            self.agent_metadata,
            fim_watch_restart_rx,
            fim_watch_restart_tx.clone(),
        ))));

        loop {
            select! {
                biased;

                _ = shutdown_receiver.recv() => {
                    let _ = fim_agent_manager.stop().await;
                    info!("Shutting Down Configuration Agent");
                    break;
                }

                _ = fim_agent_manager.start() => {
                    info!("Agent manager of configuration finished execution");
                },

                _ = sleep(Duration::from_secs(
                    self.agent_metadata
                        .get_agent_refresh_settings()
                        .refresh_cycle,
                )) => {
                    self.handle_refresh_call(Some(&fim_watch_restart_tx)).await;
                }

            }
        }
        info!("---------------------- Stopped Configuration Agent ------------------------");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if let Err(error) = self.stop_signal_sender.send(true).await {
            error!(?error, "Failed to send stop signal to Configuration Agent");
        }
        Ok(())
    }

    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
}
