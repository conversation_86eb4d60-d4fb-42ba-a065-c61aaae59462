use crate::<PERSON>Key;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::fmt::Display;

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug, Default)]
pub enum DeploymentPolicyType {
    #[serde(rename = "instant")]
    #[default]
    Instant,
    #[serde(rename = "schedule")]
    Scheduled,
}

impl Display for DeploymentPolicyType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DeploymentPolicyType::Instant => f.write_str("instant"),
            DeploymentPolicyType::Scheduled => f.write_str("scheduled"),
        }
    }
}

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug)]
pub enum DeploymentInitiation {
    #[serde(rename = "on_system_start_up")]
    OnStartUp,
    #[serde(rename = "on_next_cycle")]
    OnNextCycle,
    #[serde(rename = "recurring")]
    Recurring,
    #[serde(rename = "")]
    None,
}

impl Display for DeploymentInitiation {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DeploymentInitiation::OnStartUp => f.write_str("on_start_up"),
            DeploymentInitiation::OnNextCycle => f.write_str("on_next_cycle"),
            DeploymentInitiation::Recurring => f.write_str("recurring"),
            DeploymentInitiation::None => f.write_str(""),
        }
    }
}

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug, Default)]
pub enum RestartType {
    #[serde(rename = "no_restart")]
    #[default]
    NoRestart,
    #[serde(rename = "force_restart")]
    ForceRestart,
    #[serde(rename = "prompt_restart")]
    PromptRestart,
    #[serde(rename = "warn_restart")]
    WarnRestart,
}

impl Display for RestartType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RestartType::NoRestart => f.write_str("norestart"),
            RestartType::ForceRestart => f.write_str("forcerestart"),
            RestartType::PromptRestart => f.write_str("promptrestart"),
            RestartType::WarnRestart => f.write_str("warnrestart"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
#[serde(rename_all = "camelCase")]
pub struct DeploymentPolicy {
    pub id: PrimaryKey,
    pub name: Option<String>,
    pub display_name: Option<String>,
    pub description: Option<String>,
    #[serde(alias = "type", alias = "type")]
    pub policy_type: DeploymentPolicyType,
    #[serde(alias = "InitiateDeploymentOn")]
    pub initiate_deployment_on: Option<DeploymentInitiation>,
    pub deployment_days: Option<Value>,
    #[serde(alias = "deploymentTimeFrom")]
    pub from_datetime: Option<i32>,
    #[serde(alias = "deploymentTimeTo")]
    pub to_datetime: Option<i32>,
    #[serde(alias = "afterEveryTime")]
    pub interval_value: Option<i32>,
    #[serde(alias = "afterEveryTimeUnit")]
    pub interval_unit: Option<String>,
    pub restart_type: Option<RestartType>,
}

impl DeploymentPolicy {
    pub fn is_instant(&self) -> bool {
        self.policy_type == DeploymentPolicyType::Instant
    }
}
