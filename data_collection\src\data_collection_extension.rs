use crate::DataCollectionError;
use anyhow::Result;
use api::data_collection::send_system_data;
use async_trait::async_trait;
use logger::{error, ModuleLogger};
use serde_json::Value;
use std::fmt::Debug;
use utils::dir::get_log_dir;

pub enum ExtensionType {
    Users,
    StartUpItems,
    Softwares,
    Resources,
    RemoteConnections,
    Processes,
    NetworkInterfaces,
    ListeningPorts,
    Certificates,
    Provision,
}

#[async_trait]
pub trait DataCollectionExtension
where
    Self: Debug + Send + Sync,
{
    fn get_name(&self) -> &str;

    fn get_refresh_interval(&self) -> u64;

    fn get_endpoint_id(&self) -> i64;

    fn build_payload(&self) -> Result<Value, DataCollectionError>;

    fn collect(&mut self) -> Result<(), DataCollectionError>;

    fn logger(&self) -> ModuleLogger {
        ModuleLogger::new(
            self.get_name().to_lowercase().as_str(),
            None,
            Some(
                get_log_dir()
                    .join("extensions")
                    .join(self.get_name().to_lowercase())
                    .to_string_lossy()
                    .to_string(),
            ),
        )
    }

    async fn send(&mut self) -> Result<(), DataCollectionError> {
        Ok(send_system_data(self.build_payload()?).await?)
    }

    async fn collect_and_send(&mut self) {
        match self.collect() {
            Ok(_) => match self.send().await {
                Err(error) => {
                    error!(?error, "Failed to send data to server for {:?}", self);
                }
                _ => {}
            },
            Err(error) => {
                error!(?error, "Failed to collect data for {:?}", self);
            }
        }
    }
}
