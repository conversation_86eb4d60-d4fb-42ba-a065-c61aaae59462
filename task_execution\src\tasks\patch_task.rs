use super::attachment_downloader::AttachmentDownloader;
use crate::{
    has_commands::HasCommands, has_task::HasTask, log_task::LogTask, sync_task::SyncTask,
    TaskExecutable, TaskExecutionError,
};
use anyhow::{anyhow, Error, Result};
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{DeploymentType, FileAttachment, Patch, Task, TaskStatus},
};
use logger::{error, info, ModuleLogger};
#[cfg(windows)]
use serde::Deserialize;
use shell::ShellOutput;
use std::sync::Arc;

#[cfg(windows)]
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct HardwareReadinessResult {
    return_code: usize,
    return_reason: String,
    logging: String,
    return_result: String,
}

#[derive(Debug)]
pub struct PatchTask {
    task: Task,
    logger: Arc<ModuleLogger>,
}

impl HasCommands for Patch {
    fn get_install_command(&self) -> Option<&String> {
        None
    }

    fn get_upgrade_command(&self) -> Option<&String> {
        None
    }

    fn get_uninstall_command(&self) -> Option<&String> {
        None
    }
}

impl PatchTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            logger: Arc::new(ModuleLogger::new("patch", None, Some("patch".to_owned()))),
        }
    }

    #[cfg(target_os = "macos")]
    async fn process_dmg(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Dmg;

        let patch = self.task.patch.as_ref().unwrap();

        let dmg = Dmg::new(
            Box::new(patch.clone()) as Box<dyn HasCommands>,
            &attachment,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => dmg.install().await,
            DeploymentType::Upgrade => dmg.upgrade().await,
            DeploymentType::Uninstall => dmg.uninstall().await,
        }
    }

    #[cfg(target_os = "macos")]
    async fn process_pkg(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Pkg;

        let patch = self.task.patch.as_ref().unwrap();

        let pkg = Pkg::new(
            Box::new(patch.clone()) as Box<dyn HasCommands>,
            &attachment,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => pkg.install().await,
            DeploymentType::Upgrade => pkg.upgrade().await,
            DeploymentType::Uninstall => pkg.uninstall().await,
        }
    }

    #[cfg(target_os = "linux")]
    async fn process_deb(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Deb;

        let patch = self.task.patch.as_ref().unwrap();

        let deb = Deb::new(
            Box::new(patch.clone()) as Box<dyn HasCommands>,
            &attachment,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => deb.install().await,
            DeploymentType::Upgrade => deb.upgrade().await,
            DeploymentType::Uninstall => deb.uninstall().await,
        }
    }

    #[cfg(target_os = "linux")]
    async fn process_rpm(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Rpm;

        let patch = self.task.patch.as_ref().unwrap();

        let rpm = Rpm::new(
            Box::new(patch.clone()) as Box<dyn HasCommands>,
            &attachment,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => rpm.install().await,
            DeploymentType::Upgrade => rpm.upgrade().await,
            DeploymentType::Uninstall => rpm.uninstall().await,
        }
    }

    #[cfg(windows)]
    async fn process_windows(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Iso;
        use crate::tasks::package_executor::Msi;

        let patch = self.task.patch.as_ref().unwrap();

        if attachment.extension() == "iso" {
            let iso = Iso::new(&attachment, Box::new(self));

            match operation_type {
                DeploymentType::Install => iso.install().await,
                DeploymentType::Upgrade => iso.upgrade().await,
                DeploymentType::Uninstall => iso.uninstall().await,
            }
        } else {
            let msi = Msi::new(
                Box::new(patch.clone()) as Box<dyn HasCommands>,
                &attachment,
                Box::new(self),
            );

            match operation_type {
                DeploymentType::Install => msi.install().await,
                DeploymentType::Upgrade => msi.upgrade().await,
                DeploymentType::Uninstall => msi.uninstall().await,
            }
        }
    }

    async fn download_file(
        &self,
        mut file: FileAttachment,
    ) -> Result<FileAttachment, TaskExecutionError> {
        if file.ref_name.len() > 0 {
            file.zirozen_download_url = Some(format!("/patch/download/patch/{}", file.ref_name));
        }
        AttachmentDownloader::new(file, Box::new(self), None)
            .download()
            .await
    }

    async fn process_file(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<TaskResult> {
        let mut task_result = TaskResult::default();
        let patch = self.task.patch.as_ref().unwrap();

        self.write_task_log(
            format!(
                "Starting to {} {}",
                operation_type,
                patch.name.as_ref().unwrap_or(&"Unknow Patch".to_owned())
            ),
            None,
        )
        .await;

        let extension = attachment.extension().to_owned();

        let output = match extension.as_str() {
            #[cfg(target_os = "macos")]
            "dmg" => self.process_dmg(attachment, operation_type).await,
            #[cfg(target_os = "macos")]
            "pkg" => self.process_pkg(attachment, operation_type).await,
            #[cfg(target_os = "linux")]
            "deb" => self.process_deb(attachment, operation_type).await,
            #[cfg(target_os = "linux")]
            "rpm" => self.process_rpm(attachment, operation_type).await,
            #[cfg(target_os = "windows")]
            "msu" | "cab" | "mst" | "msi" | "msp" | "exe" | "iso" => {
                self.process_windows(attachment, operation_type).await
            }
            _ => {
                return Err(anyhow!(TaskExecutionError::UnsupportedFileExtension(
                    extension.to_owned()
                )))
            }
        }?;

        // if msu file then consider non zero code as well
        if extension.as_str() == "msu" || extension.as_str() == "iso" {
            if output.exit_code == 0 || output.exit_code == 3010 || output.exit_code == 2359302 {
                self.write_task_log(
                    format!(
                        "{} file execution exit code is {} and is considered as succeed",
                        extension.to_uppercase(),
                        output.exit_code
                    ),
                    None,
                )
                .await;
                info!(
                    "{} file execution exit code is {} and is considered as succeed",
                    extension.to_uppercase(),
                    output.exit_code
                );
                if output.exit_code == 3010 {
                    task_result.status = TaskStatus::SuccessWithRebootRequired;
                    task_result.exit_code = output.exit_code;
                } else {
                    task_result.status = TaskStatus::Success;
                    task_result.exit_code = output.exit_code;
                }
            } else {
                task_result.exit_code = output.exit_code;
                task_result.status = TaskStatus::Failed
            }
            return Ok(task_result);
        }

        if output.failed() {
            task_result.exit_code = output.exit_code;
            task_result.status = TaskStatus::Failed
        } else {
            task_result.exit_code = output.exit_code;
            task_result.status = TaskStatus::Success;
        }

        Ok(task_result)
    }

    #[cfg(windows)]
    async fn validate_hardware_requirements_for_iso(&self) -> Result<HardwareReadinessResult> {
        use super::command_executor::CommandExecutor;
        use logger::debug;
        use tokio::fs;

        let file_content = include_bytes!("./HardwareReadiness.ps1");
        let script_path = self.get_task_dir().join("HardwareReadiness.ps1");
        match fs::write(&script_path, file_content).await {
            Ok(_) => {
                debug!("HardwareReadiness script has been written successfully.");
                self.write_task_log("Checking Hardware Requirements".to_owned(), None)
                    .await;
            }
            Err(error) => {
                error!(?error, "Failed to write HardwareReadiness script");
                self.write_task_log(
                    format!("Failed to write HardwareReadiness script {:?}", error),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        };

        let output = match CommandExecutor::new_script(script_path, Box::new(self))
            .capture()
            .execute()
            .await
        {
            Ok(output) => output,
            Err(error) => {
                error!(?error, "Failed to execute hardware readiness check scirpt");
                self.write_task_log(
                    format!("Failed to execute HardwareReadiness script {:?}", error),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        };
        self.write_task_log(format!("Hardware Check result {}", output.output), None)
            .await;

        let result: HardwareReadinessResult = match serde_json::from_str(&output.output) {
            Ok(result) => result,
            Err(error) => {
                error!(?error, "Failed to parse Hardware Check Result");
                self.write_task_log(
                    format!("Failed to parse HardwareReadiness result {:?}", error),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        };

        Ok(result)
    }
}

impl HasTask for PatchTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for PatchTask {
    fn get_logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for PatchTask {}

#[async_trait]
impl TaskExecutable for PatchTask {
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            format!("Initiating execution of Package {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        // if no package found return error
        if self.task.patch.is_none() {
            error!("No patch found for {:?}", self.task);
            self.write_task_log("No patch found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        // if no deployment found return error
        if self.task.deployment.is_none() {
            error!("No deployment found for {:?}", self.task);
            self.write_task_log("No deployment found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        let patch = self.task.patch.as_ref().unwrap();

        let deployment = self.task.deployment.as_ref().unwrap();

        self.write_task_log(
            format!(
                "{} of {} has started processing",
                deployment.deployment_type,
                patch
                    .display_name
                    .as_ref()
                    .unwrap_or(&"Unknown Patch".to_owned())
            ),
            None,
        )
        .await;

        let files_to_process = {
            cfg_if! {
                if #[cfg(windows)] {
                    // if upgrades then select only iso
                    if patch.patch_update_category.as_ref().is_some_and(|value| value.to_lowercase() == "upgrades") {
                        let files = patch
                            .download_files
                            .iter()
                            .filter(|item| item.extension() == "iso")
                            .collect::<Vec<&FileAttachment>>();

                        if files.len() > 1 {
                            self.write_task_log("Unable to determine file to process".to_owned(), Some("ERROR")).await;
                            task_result.status = TaskStatus::Failed;
                            task_result.exit_code = 99;
                            return Ok(task_result);
                        }
                        files
                    } else if patch.is_third_party.is_some_and(|value| value) {
                        patch.download_files.iter().collect()
                    } else {
                        let patch_only_executable_file = patch
                            .download_files
                            .iter()
                            .filter(|item| item.patch_only_file_to_install.is_some_and(|v| v))
                            .collect::<Vec<&FileAttachment>>();

                        if patch_only_executable_file.len() > 0 {
                            patch_only_executable_file
                        } else {
                            let msu_files = patch
                                .download_files
                                .iter()
                                .filter(|item| item.extension() == "msu")
                                .collect::<Vec<&FileAttachment>>();
                            if msu_files.len() > 0 {
                                msu_files
                            } else {
                                let cabs = patch
                                    .download_files
                                    .iter()
                                    .filter(|item| item.extension() == "cab")
                                    .collect::<Vec<&FileAttachment>>();

                                if cabs.len() > 0 {
                                    cabs
                                } else {
                                    patch.download_files.iter().collect::<Vec<&FileAttachment>>()
                                }
                            }
                        }
                    }
                } else {
                    if patch.product_type == Some("macOsVersionUpdate".to_owned()) {
                        patch
                            .download_files
                            .iter()
                            .filter(|item| item.real_name == "InstallAssistant.pkg".to_owned())
                            .collect::<Vec<&FileAttachment>>()
                    } else {
                        patch.download_files.iter().collect()
                    }
                }
            }
        }
        .iter()
        .map(|i| i.to_owned())
        .filter(|item| {
            item.public_url.as_ref().is_some_and(|i| !i.is_empty()) || !item.ref_name.is_empty()
        })
        .collect::<Vec<&FileAttachment>>();

        let mut task_result = TaskResult::default();

        for attachment in files_to_process {
            if attachment.extension() == "iso" {
                #[cfg(windows)]
                {
                    let hardware_check_result =
                        match self.validate_hardware_requirements_for_iso().await {
                            Ok(result) => result,
                            Err(error) => {
                                error!(?error, "Error in receiving hardware requirement result");
                                task_result.status = TaskStatus::Failed;
                                task_result.exit_code = 199;
                                return Ok(task_result);
                            }
                        };
                    if hardware_check_result.return_code != 0
                        && hardware_check_result.return_result.to_lowercase() == "capable"
                    {
                        self.write_task_log(
                            format!("Failed to meet the minimum requirements to install the given upgrade\n Failed Requirements:\n {}", hardware_check_result.return_reason),
                            Some("ERROR"),
                        )
                        .await;
                        task_result.status = TaskStatus::Failed;
                        task_result.exit_code = 199;
                        return Ok(task_result);
                    } else {
                        self.write_task_log(
                            format!(
                                "Successfully verified all the minimum requirements with output:\n {}",
                                hardware_check_result.logging
                            ),
                            None,
                        )
                        .await;
                    }
                }
            }

            let downloaded_attachment = match self.download_file(attachment.to_owned()).await {
                Ok(attachment) => attachment,
                Err(error) => {
                    error!(?error, "Failed to download patch file {:?}", attachment);
                    task_result.exit_code = 99;
                    task_result.status = TaskStatus::Failed;
                    task_result.output = format!(
                        "Failed to download patch file {:?} with error {:?}",
                        attachment, error
                    );
                    break;
                }
            };

            match self
                .process_file(
                    downloaded_attachment.clone(),
                    deployment.deployment_type.to_owned(),
                )
                .await
            {
                Ok(r) => {
                    if r.status == TaskStatus::Failed {
                        error!(
                            "Attachment {:?} processed with result {:?}",
                            downloaded_attachment, r
                        );
                        task_result = r;
                        if patch.should_succeed_on_single_file_installation == false {
                            break;
                        }
                    } else {
                        info!(
                            "Attachment {:?} processed with result {:?}",
                            downloaded_attachment, r.exit_code
                        );
                        task_result = r;
                        if patch.should_succeed_on_single_file_installation {
                            break;
                        }
                    }
                }
                Err(error) => {
                    error!(?error, "Failed to process file {:?}", downloaded_attachment);
                    task_result.status = TaskStatus::Failed;
                    task_result.exit_code = 99;
                    task_result.output = format!(
                        "Failed to process file {:?} with error {:?}",
                        downloaded_attachment, error
                    );
                    if patch.should_succeed_on_single_file_installation == false {
                        break;
                    }
                }
            };
        }

        Ok(task_result)
    }
}
