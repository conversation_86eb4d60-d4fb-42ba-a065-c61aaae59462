[package]
name = "auth_event"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
utils = { path = "../utils" }
database = { path = "../database" }
thiserror = "2.0.4"
anyhow = { version = "1.0.94", features = ["backtrace"] }
cfg-if = "1.0.0"
serde = "1.0.219"
async-trait = "0.1.83"
chrono = { version = "0.4.39", features = ["alloc"] }
tokio = { version = "1.43.0", features = ["full", "tracing"] }


[target.'cfg(target_os = "linux")'.dependencies]
inotify = "0.11"
futures = "0.3.31"
regex = "1"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.59", features = [
  "Win32_Foundation",
  "Win32_System_EventLog",
  "Win32_System_WindowsProgramming",
] }
