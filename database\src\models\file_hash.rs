use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DB};
use chrono::{DateTime, Local};
use logger::{error, trace};
use md5::{Digest as Md5Digest, Md5};
use serde::{Deserialize, Serialize};
use sha1::Sha1;
use sha2::Sha256;
use std::{
    fs::File,
    io::{<PERSON>ufRead<PERSON>, Read},
    path::PathBuf,
    time::Instant,
};

#[derive(Debu<PERSON>, Default, Serialize, Deserialize, Clone)]
pub struct FileHash {
    md5: String,
    sha1: String,
    sha256: String,
    file_path: PrimaryKey,
    file_size: u64,
    last_modified: i64,
}

impl From<&str> for FileHash {
    fn from(value: &str) -> Self {
        FileHash::generate_hashes_and_persist(value)
    }
}

impl From<String> for FileHash {
    fn from(value: String) -> Self {
        FileHash::generate_hashes_and_persist(&value)
    }
}

impl FileHash {
    pub fn file_path(&self) -> String {
        self.file_path.to_string()
    }

    pub fn md5(&self) -> &str {
        &self.md5
    }

    pub fn sha1(&self) -> &str {
        &self.sha1
    }

    pub fn sha256(&self) -> &str {
        &self.sha256
    }

    pub async fn for_path(path: String) -> Option<FileHash> {
        let hash = FileHash {
            file_path: path.into(),
            ..Default::default()
        };

        match hash.load().await {
            Ok(hash) => Some(hash),
            Err(_) => None,
        }
    }

    pub async fn for_paths(paths: Vec<String>) -> Vec<FileHash> {
        match DB
            .query("select * from file_hash where file_path in $paths")
            .bind(("paths", paths.join(",")))
            .await
        {
            Ok(mut value) => value.take(0).unwrap_or_default(),
            Err(error) => {
                error!(?error, "Failed to get all file_hahses");
                vec![]
            }
        }
    }

    pub fn generate_hashes(file_path: &str) -> Option<FileHash> {
        if file_path.is_empty() {
            return None;
        }
        trace!("Generating hash for file {}", file_path);
        let file_path_buf = PathBuf::from(file_path);
        if file_path_buf.exists() == false {
            return None;
        }

        let time = Instant::now();
        let mut buffer = [0u8; 8192];
        let mut fs_hash = FileHash::default();

        let mut file = match File::open(file_path) {
            Ok(f) => {
                match f.metadata() {
                    Ok(metadata) => {
                        fs_hash.file_path = file_path.to_owned().into();
                        fs_hash.file_size = metadata.len();
                        fs_hash.last_modified =
                            Into::<DateTime<Local>>::into(metadata.modified().unwrap()).timestamp();
                    }
                    _ => {}
                }
                BufReader::new(f)
            }
            Err(_) => {
                error!("Error opening file: {}", file_path);
                return None;
            }
        };

        let mut md5_hasher = Md5::new();
        let mut sha1_hasher = Sha1::new();
        let mut sha256_hasher = Sha256::new();

        while let Ok(bytes_read) = file.read(&mut buffer[..]) {
            if bytes_read == 0 {
                break;
            }

            md5_hasher.update(&buffer[..bytes_read]);
            sha1_hasher.update(&buffer[..bytes_read]);
            sha256_hasher.update(&buffer[..bytes_read]);
        }
        fs_hash.md5 = hex::encode(md5_hasher.finalize());
        fs_hash.sha1 = hex::encode(sha1_hasher.finalize());
        fs_hash.sha256 = hex::encode(sha256_hasher.finalize());

        trace!(
            "Took {:?} time to compute hash for file {}",
            time.elapsed(),
            file_path
        );
        Some(fs_hash)
    }

    pub fn generate_hashes_and_persist(file_path: &str) -> FileHash {
        let file_hash = FileHash {
            file_path: file_path.to_string().into(),
            ..Default::default()
        };

        let cloned_file_hash = file_hash.clone();

        match futures::executor::block_on(async move { file_hash.load().await }) {
            Ok(file_hash) => return file_hash,
            _ => {}
        };

        match FileHash::generate_hashes(file_path) {
            Some(fs_hash) => match futures::executor::block_on(async { fs_hash.persist().await }) {
                Ok(fs_hash) => fs_hash,
                Err(_) => cloned_file_hash,
            },
            None => cloned_file_hash,
        }
    }
}

impl HasPrimaryKey for FileHash {
    fn table_name(&self) -> &str {
        return "file_hash";
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.file_path
    }
}

impl Model for FileHash {}
