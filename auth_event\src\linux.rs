use crate::{<PERSON>, AuthEvent, AuthEventError, AuthEventMonitor};
use async_trait::async_trait;
use chrono::{DateTime, Datelike, Local, TimeZone, Utc};
use futures::StreamExt;
use inotify::{Inotify, WatchMask};
use logger::{debug, error};
use regex::Regex;
use std::collections::HashMap;
use tokio::fs::File;
use tokio::io::{AsyncBufReadExt, AsyncSeekExt, BufReader, SeekFrom};
use tokio::sync::mpsc::Sender;

pub struct LinuxMonitor;

#[async_trait]
impl AuthEventMonitor for LinuxMonitor {
    async fn monitor(
        &mut self,
        mut shutdown_recv: Receiver<()>,
        sender: Sender<AuthEvent>,
    ) -> Result<(), AuthEventError> {
        let path = "/var/log/auth.log";

        let mut file = match File::open(path).await {
            Ok(file) => file,
            Err(error) => {
                error!(?error, "Failed to open file {}", path);
                shutdown_recv.recv().await;
                return Err(AuthEventError::IOError(error));
            }
        };

        match file.seek(SeekFrom::End(0)).await {
            Ok(_) => {}
            Err(error) => {
                error!(?error, "Failed to seek to end of file {}", path);
                shutdown_recv.recv().await;
                return Err(AuthEventError::IOError(error));
            }
        };

        let mut reader = BufReader::new(file);

        reader.seek(SeekFrom::End(0)).await?;

        let mut lines = reader.lines();

        let inotify = Inotify::init()?;

        match inotify.watches().add(path, WatchMask::MODIFY) {
            Ok(_) => {}
            Err(error) => {
                error!(?error, "Failed to add watch to inotify");
                shutdown_recv.recv().await;
                return Err(AuthEventError::IOError(error));
            }
        };

        let mut buffer = [0u8; 4096];

        let mut event_stream = match inotify.into_event_stream(&mut buffer) {
            Ok(stream) => stream,
            Err(error) => {
                error!(?error, "Failed to create event stream");
                shutdown_recv.recv().await;
                return Err(AuthEventError::IOError(error));
            }
        };

        let re_failed = Regex::new(
            r"sshd.*Failed password for (invalid user )?(?P<user>\w+) from (?P<ip>[\d\.]+)",
        )
        .unwrap();

        let re_accepted =
            Regex::new(r"sshd.*Accepted \w+ for (?P<user>\w+) from (?P<ip>[\d\.]+)").unwrap();
        let re_login = Regex::new(r"sshd.*session opened for user (?P<user>\w+)").unwrap();
        let re_logout = Regex::new(r"sshd.*session closed for user (?P<user>\w+)").unwrap();

        let mut active_ips: HashMap<String, String> = HashMap::new();

        while let Some(Ok(_)) = event_stream.next().await {
            while let Ok(Some(line)) = lines.next_line().await {
                if let Some(timestamp) = extract_utc_timestamp(&line) {
                    if let Some(caps) = re_failed.captures(&line) {
                        let user = caps["user"].to_owned();

                        let ip = caps["ip"].to_owned();

                        let mut event = AuthEvent::new(Action::LoginFailed, user, ip);

                        event.set_event_time(timestamp);

                        event.generate_id();

                        debug!("Received Event {:?}", event);

                        match sender.send(event).await {
                            Ok(()) => {}
                            Err(error) => {
                                error!(?error, "Failed to send event in channel");
                            }
                        };
                    } else if let Some(caps) = re_accepted.captures(&line) {
                        active_ips.insert(caps["user"].to_string(), caps["ip"].to_string());
                    } else if let Some(caps) = re_login.captures(&line) {
                        let user = caps["user"].to_owned();

                        let ip = active_ips.get(&user).map_or("unknown", |ip| ip.as_str());

                        let mut event = AuthEvent::new(Action::Login, user, ip.to_owned());

                        event.set_event_time(timestamp);

                        event.generate_id();

                        debug!("Received Event {:?}", event);

                        match sender.send(event).await {
                            Ok(()) => {}
                            Err(error) => {
                                error!(?error, "Failed to send event in channel");
                            }
                        };
                    } else if let Some(caps) = re_logout.captures(&line) {
                        let user = caps["user"].to_owned();

                        let ip = active_ips
                            .remove(&user)
                            .unwrap_or_else(|| "unknown".to_string());

                        let mut event = AuthEvent::new(Action::Logout, user, ip);

                        event.set_event_time(timestamp);

                        event.generate_id();

                        debug!("Received Event {:?}", event);

                        match sender.send(event).await {
                            Ok(()) => {}
                            Err(error) => {
                                error!(?error, "Failed to send event in channel");
                            }
                        };
                    }
                }
            }
        }

        Ok(())
    }
}

fn extract_utc_timestamp(line: &str) -> Option<i64> {
    let re =
        Regex::new(r"^(?P<month>\w{3})\s+(?P<day>\d{1,2})\s+(?P<time>\d{2}:\d{2}:\d{2})").ok()?;
    let caps = re.captures(line)?;
    let month_str = caps.name("month")?.as_str();
    let day = caps.name("day")?.as_str().parse::<u32>().ok()?;
    let time_str = caps.name("time")?.as_str();
    let year = Utc::now().year();
    let full_str = format!("{} {} {} {}", year, month_str, day, time_str);
    let naive = chrono::NaiveDateTime::parse_from_str(&full_str, "%Y %b %d %H:%M:%S").ok()?;
    let utc: DateTime<Utc> = Local.from_local_datetime(&naive).unwrap().into();
    Some(utc.timestamp())
}
