use crate::{<PERSON>, AuthEvent, AuthEventError, AuthEventMonitor};
use async_trait::async_trait;
use chrono::{DateTime, Datelike, Local, TimeZone, Utc};
use futures::StreamExt;
use inotify::{Inotify, WatchMask};
use logger::{debug, error};
use regex::Regex;
use std::collections::HashMap;
use tokio::fs::File;
use tokio::io::{AsyncBufReadExt, AsyncSeekExt, BufReader, SeekFrom};
use tokio::sync::mpsc::Sender;
use utils::shutdown::get_shutdown_signal;

pub struct LinuxMonitor;

#[async_trait]
impl AuthEventMonitor for LinuxMonitor {
    async fn monitor(&mut self, sender: Sender<AuthEvent>) -> Result<(), AuthEventError> {
        let mut shutdown_recv = get_shutdown_signal();
        let path = "/var/log/auth.log";

        let mut file = match File::open(path).await {
            Ok(file) => file,
            Err(error) => {
                error!(?error, "Failed to open file {}", path);
                shutdown_recv.recv().await.ok();
                return Err(AuthEventError::IOError(error));
            }
        };

        match file.seek(SeekFrom::End(0)).await {
            Ok(_) => {}
            Err(error) => {
                error!(?error, "Failed to seek to end of file {}", path);
                shutdown_recv.recv().await.ok();
                return Err(AuthEventError::IOError(error));
            }
        };

        let mut reader = BufReader::new(file);

        reader.seek(SeekFrom::End(0)).await?;

        let mut lines = reader.lines();

        let inotify = Inotify::init()?;

        match inotify.watches().add(path, WatchMask::MODIFY) {
            Ok(_) => {}
            Err(error) => {
                error!(?error, "Failed to add watch to inotify");
                shutdown_recv.recv().await.ok();
                return Err(AuthEventError::IOError(error));
            }
        };

        let mut buffer = [0u8; 4096];

        let mut event_stream = match inotify.into_event_stream(&mut buffer) {
            Ok(stream) => stream,
            Err(error) => {
                error!(?error, "Failed to create event stream");
                shutdown_recv.recv().await.ok();
                return Err(AuthEventError::IOError(error));
            }
        };

        let re_failed = Regex::new(
            r"sshd.*Failed password for (invalid user )?(?P<user>\w+) from (?P<ip>[\d\.]+)",
        )
        .unwrap();

        let re_accepted =
            Regex::new(r"sshd.*Accepted \w+ for (?P<user>\w+) from (?P<ip>[\d\.]+)").unwrap();
        let re_login = Regex::new(r"sshd.*session opened for user (?P<user>\w+)").unwrap();
        let _re_logout = Regex::new(r"sshd.*session closed for user (?P<user>\w+)").unwrap();

        let mut active_ips: HashMap<String, String> = HashMap::new();

        while let Some(Ok(_)) = event_stream.next().await {
            while let Ok(Some(line)) = lines.next_line().await {
                if let Some(timestamp) = extract_utc_timestamp(&line) {
                    if let Some(caps) = re_failed.captures(&line) {
                        let user = caps["user"].to_owned();

                        let ip = caps["ip"].to_owned();

                        let mut event = AuthEvent::new(Action::LoginFailed, user, ip);

                        event.set_event_time(timestamp);

                        event.generate_id();

                        debug!("Received Event {:?}", event);

                        match sender.send(event).await {
                            Ok(()) => {}
                            Err(error) => {
                                error!(?error, "Failed to send event in channel");
                            }
                        };
                    } else if let Some(caps) = re_accepted.captures(&line) {
                        active_ips.insert(caps["user"].to_string(), caps["ip"].to_string());
                    } else if let Some(caps) = re_login.captures(&line) {
                        let user = caps["user"].to_owned();

                        let ip = active_ips.get(&user).map_or("unknown", |ip| ip.as_str());

                        let mut event = AuthEvent::new(Action::Login, user, ip.to_owned());

                        event.set_event_time(timestamp);

                        event.generate_id();

                        debug!("Received Event {:?}", event);

                        match sender.send(event).await {
                            Ok(()) => {}
                            Err(error) => {
                                error!(?error, "Failed to send event in channel");
                            }
                        };
                    }
                    //  else if let Some(caps) = re_logout.captures(&line) {
                    //     let user = caps["user"].to_owned();

                    //     let ip = active_ips
                    //         .remove(&user)
                    //         .unwrap_or_else(|| "unknown".to_string());

                    //     let mut event = AuthEvent::new(Action::Logout, user, ip);

                    //     event.set_event_time(timestamp);

                    //     event.generate_id();

                    //     debug!("Received Event {:?}", event);

                    //     match sender.send(event).await {
                    //         Ok(()) => {}
                    //         Err(error) => {
                    //             error!(?error, "Failed to send event in channel");
                    //         }
                    //     };
                    // }
                }
            }
        }

        Ok(())
    }
}

fn extract_utc_timestamp(line: &str) -> Option<i64> {
    // Format: 2025-06-30T15:12:29.503725+05:30
    let iso_re =
        Regex::new(r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:[+-]\d{2}:\d{2}|Z)?").unwrap();

    // Format: 2025-06-30 15:12:45
    let iso_space_re = Regex::new(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}").ok()?;

    // Format: Jun 30 15:12:45
    let syslog_re = Regex::new(r"\b[A-Z][a-z]{2} +\d{1,2} \d{2}:\d{2}:\d{2}\b").ok()?;

    // Format: 30/Jun/2025:15:12:45 +0000
    let apache_re = Regex::new(r"\d{2}/[A-Z][a-z]{2}/\d{4}:\d{2}:\d{2}:\d{2} [+-]\d{4}").ok()?;

    for mat in iso_re.find_iter(line) {
        if let Ok(dt) = DateTime::parse_from_rfc3339(mat.as_str()) {
            let utc: DateTime<Utc> = dt.into();
            return Some(utc.timestamp());
        }
    }

    for mat in iso_space_re.find_iter(line) {
        if let Ok(dt) = chrono::NaiveDateTime::parse_from_str(mat.as_str(), "%Y-%m-%d %H:%M:%S") {
            let utc: DateTime<Utc> = Local.from_local_datetime(&dt).unwrap().into();
            return Some(utc.timestamp());
        }
    }

    for mat in syslog_re.find_iter(line) {
        let year = Local::now().year();
        let ts_with_year = format!("{} {}", year, mat.as_str());
        if let Ok(dt) = chrono::NaiveDateTime::parse_from_str(&ts_with_year, "%Y %b %e %T") {
            let utc: DateTime<Utc> = Local.from_local_datetime(&dt).unwrap().into();
            return Some(utc.timestamp());
        }
    }

    for mat in apache_re.find_iter(line) {
        if let Ok(dt) = DateTime::parse_from_str(mat.as_str(), "%d/%b/%Y:%H:%M:%S %z") {
            let utc: DateTime<Utc> = dt.into();
            return Some(utc.timestamp());
        }
    }

    return Some(Utc::now().timestamp());
}
