use crate::{tasks::attachment_downloader::AttachmentDownloader, TaskExecutable};
use anyhow::Result;
use api::patch::{get_linux_latest_package_info, send_patch_discovered_data};
use database::models::FileAttachment;
use logger::{debug, error, info};
use rayon::iter::{IntoParallelRefIterator, ParallelIterator};
use serde::Deserialize;
use serde_json::{json, Number, Value};
use std::{collections::HashMap, fs::File};
use tokio::fs;
use tokio::time::Instant;
use utils::shutdown::is_system_running;
use utils::{cpu::concurrency_cores, dir::get_patch_dir};
use version_compare::Version;

#[derive(Debug, Default, Deserialize)]
struct SoftwareInfo {
    pub id: Option<i32>,
    pub name: String,
    pub version: String,
}

impl SoftwareInfo {
    pub fn from_value(value: &Value) -> Vec<SoftwareInfo> {
        let installed_software_arr = value.as_array().unwrap();

        let mut software_infos = vec![];

        for item in installed_software_arr {
            software_infos.push(item.into());
        }

        software_infos
    }
}

impl From<&Value> for SoftwareInfo {
    fn from(value: &Value) -> Self {
        serde_json::from_value::<Self>(value.to_owned()).unwrap_or_default()
    }
}

impl From<Value> for SoftwareInfo {
    fn from(value: Value) -> Self {
        serde_json::from_value::<Self>(value).unwrap_or_default()
    }
}

pub struct LinuxPatchFinder<'a> {
    endpoint_id: i64,
    installed_softwares: Vec<SoftwareInfo>,
    available_softwares: HashMap<String, Vec<SoftwareInfo>>,
    files: Vec<FileAttachment>,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> LinuxPatchFinder<'a> {
    pub fn new(
        endpoint_id: i64,
        installed_version: &Value,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            endpoint_id,
            installed_softwares: SoftwareInfo::from_value(installed_version),
            files: vec![],
            available_softwares: HashMap::new(),
            task,
        }
    }

    async fn get_all_attachments(&self) -> Result<Vec<FileAttachment>> {
        let patch_directory = get_patch_dir();

        fs::create_dir_all(&patch_directory).await?;

        let packages = get_linux_latest_package_info(self.endpoint_id).await?;

        let mut file_attachments = vec![];

        for (_name, file) in packages.iter() {
            if let Ok(attachment) = AttachmentDownloader::new(
                FileAttachment {
                    real_name: file.to_owned(),
                    ref_name: file.to_owned(),
                    zirozen_download_url: Some(format!("/patch/download/patchdb/{}", file)),
                    ..Default::default()
                },
                self.task.clone(),
                Some(patch_directory.clone()),
            )
            .download()
            .await
            {
                self.task
                    .write_task_log(
                        format!(
                            "Attachment {} downloaded successfully",
                            attachment.real_name
                        ),
                        None,
                    )
                    .await;
                file_attachments.push(attachment);
            }
        }

        Ok(file_attachments)
    }

    fn process_file(attachment: FileAttachment) -> Vec<SoftwareInfo> {
        let file_path = attachment.path_to_file();

        debug!("Reading file {}", file_path.display());

        let reader = File::open(file_path);

        if reader.is_ok() {
            let mut json = match serde_json::from_reader(reader.unwrap()) {
                Ok(value) => value,
                Err(error) => {
                    error!(?error, "Failed to deserialize file content");
                    Value::Null
                }
            };

            json.as_object_mut()
                .unwrap()
                .into_iter()
                .map(|(id, item)| {
                    item.as_object_mut().unwrap().insert(
                        "id".to_owned(),
                        Value::Number(Number::from(
                            id.parse::<i32>().expect("Failed to parse number"),
                        )),
                    );

                    item.to_owned().into()
                })
                .collect::<Vec<SoftwareInfo>>()
        } else {
            error!(error = ?reader.err().unwrap(), "Failed to open file {:?}", attachment);
            vec![]
        }
    }

    async fn collect_entries(&mut self) -> Result<HashMap<String, Vec<SoftwareInfo>>> {
        info!("Collecting available patches to scan");
        let mut software_map: HashMap<String, Vec<SoftwareInfo>> = HashMap::new();

        let allowed_threads = concurrency_cores(30);

        debug!("Using {} cores for rayon thread pool", allowed_threads);

        let logger = self.task.get_logger().clone();

        let pool = rayon::ThreadPoolBuilder::new()
            .num_threads(allowed_threads)
            .build()
            .unwrap();

        let result = pool.install(|| {
            logger.with(|| {
                self.files
                    .par_iter()
                    .take_any_while(|_| is_system_running())
                    .flat_map_iter(|file| LinuxPatchFinder::process_file(file.clone()))
                    .collect::<Vec<SoftwareInfo>>()
            })
        });

        for software_info in result {
            if software_map.contains_key(&software_info.name) {
                software_map
                    .get_mut(&software_info.name)
                    .unwrap()
                    .push(software_info);
            } else {
                software_map.insert(software_info.name.clone(), vec![software_info]);
            }
        }

        // let mut files_future = self
        //     .files
        //     .iter()
        //     .map(|file| LinuxPatchFinder::process_file(file.clone(), self.semaphore.clone()))
        //     .collect::<FuturesOrdered<_>>();

        // while let Some(response) = files_future.next().await {
        //     let mut response_stream = stream::iter(response);
        //     while let Some(software_info) = response_stream.next().await {
        //         if software_map.contains_key(&software_info.name) {
        //             software_map
        //                 .get_mut(&software_info.name)
        //                 .unwrap()
        //                 .push(software_info);
        //         } else {
        //             software_map.insert(software_info.name.clone(), vec![software_info]);
        //         }
        //     }
        // }

        self.task
            .write_task_log(
                format!("Collected total {} entries", software_map.len()),
                None,
            )
            .await;

        Ok(software_map)
    }

    async fn get_missing_ids(&self) -> Result<Vec<i32>> {
        info!("Scanning all available softwares to check if anything missing");

        let mut missing_uuids: Vec<i32> = vec![];

        for installed_software in &self.installed_softwares {
            let installed_version = Version::from(&installed_software.version);
            if let Some(installed_version) = Version::from(&installed_software.version) {
                if installed_version.to_string().is_empty() {
                    error!("Installed version is empty for {:?}", installed_software);
                    continue;
                }

                if let Some(available_software_list) =
                    self.available_softwares.get(&installed_software.name)
                {
                    let mut missing_id = None;

                    let mut max_available_version = installed_version.clone();

                    for available_software in available_software_list {
                        let available_version = Version::from(&available_software.version);

                        if let Some(available_version) = available_version {
                            if available_version.to_string().is_empty() {
                                error!(
                                    "Available software version is invalid of {:?}",
                                    available_software
                                );
                                continue;
                            }
                            if available_version > max_available_version {
                                debug!(
                                    "[{}: {}] Compared version {} with {} and {} is selected",
                                    available_software.id.unwrap_or_default(),
                                    available_software.name,
                                    available_version.to_string(),
                                    max_available_version.to_string(),
                                    available_version.to_string()
                                );
                                max_available_version = available_version;
                                if available_software.id.as_ref().is_none() {
                                    error!(
                                        "ID is not found in available software entry {:?}",
                                        available_software
                                    );
                                }
                                missing_id = available_software.id;
                            } else {
                                debug!(
                                    "Available version {} is less than installed version {}",
                                    available_version.to_string(),
                                    installed_version.to_string()
                                )
                            }
                        }
                    }
                    if missing_id.is_some() {
                        debug!("Got Missing patch with ID {}", missing_id.as_ref().unwrap());
                        missing_uuids.push(missing_id.unwrap());
                    }
                } else {
                    debug!(
                        "No available software found for {}",
                        installed_software.name
                    );
                }
            } else {
                error!(
                    "Skipping [{}] because installed version is not parsed {:?}",
                    installed_software.name, installed_version
                );
                self.task
                    .write_task_log(
                        format!(
                            "Skipping [{}] because installed version is not parsed {:?}",
                            installed_software.name, installed_version
                        ),
                        Some("ERROR"),
                    )
                    .await;
            }
        }

        info!(
            "Found total {} missing patches {:?}",
            missing_uuids.len(),
            missing_uuids
        );

        Ok(missing_uuids)
    }

    pub async fn scan(mut self) -> Result<()> {
        let time = Instant::now();

        self.files = self.get_all_attachments().await?;

        self.available_softwares = self.collect_entries().await?;

        let endpoint_id = self.endpoint_id;

        let missing_uuids = self.get_missing_ids().await?;

        send_patch_discovered_data(
            endpoint_id,
            json!({
                "linuxMissingPatches": missing_uuids,
            }),
        )
        .await?;

        debug!("Time taken for full scanning {:?}", time.elapsed());

        Ok(())
    }
}
