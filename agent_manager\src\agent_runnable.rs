use anyhow::Result;
use async_trait::async_trait;
use logger::ModuleLogger;

#[async_trait]
pub trait AgentRunnable: Send + Sync + 'static {
    fn get_name(&self) -> &str;

    async fn start(&self) -> Result<()>;

    async fn stop(&self) -> Result<()>;

    async fn restart(&self) -> Result<()>;

    fn logger(&self) -> ModuleLogger {
        ModuleLogger::new(self.get_name().to_lowercase().as_str(), None, None)
    }
}
