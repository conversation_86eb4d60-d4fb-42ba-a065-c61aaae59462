use super::RelationshipItem;
use crate::{PatchStatus, WindowsUpdate, WindowsXmlCheckerError};
use dashmap::DashMap;
use logger::{debug, error};
use serde::{Deserialize, Deserializer};
use std::sync::Arc;

fn flatten_relationship_items<'de, D>(
    deserializer: D,
) -> Result<Option<Vec<RelationshipItem>>, D::Error>
where
    D: Deserializer<'de>,
{
    /// Represents <list>...</list>
    #[derive(Deserialize)]
    struct List {
        // default allows empty list
        #[serde(default, rename = "$value")]
        relationship_items: Vec<RelationshipItem>,
    }
    let items = List::deserialize(deserializer)?;
    Ok(Some(items.relationship_items))
}

pub struct RelationEvaluationConfig {
    pub level: usize,
    scanned_output: Arc<String>,
    update_history: Arc<String>,
    result_cache: Arc<DashMap<String, u8>>,
}

impl RelationEvaluationConfig {
    pub fn new(
        level: usize,
        scanned_output: Arc<String>,
        update_history: Arc<String>,
        result_cache: Arc<DashMap<String, u8>>,
    ) -> Self {
        Self {
            level,
            scanned_output,
            update_history,
            result_cache,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct Relationships {
    #[serde(default, deserialize_with = "flatten_relationship_items")]
    prerequisites: Option<Vec<RelationshipItem>>,
    #[serde(default, deserialize_with = "flatten_relationship_items")]
    bundled_updates: Option<Vec<RelationshipItem>>,
    #[serde(default, deserialize_with = "flatten_relationship_items")]
    _superseded_updates: Option<Vec<RelationshipItem>>,
}

impl Relationships {
    pub fn has_prerequisites(&self) -> bool {
        self.prerequisites.is_some()
    }

    pub fn has_bundled_updates(&self) -> bool {
        self.bundled_updates.is_some()
    }

    fn evaluate_uuid(uuid: &str, config: &RelationEvaluationConfig) -> PatchStatus {
        if let Some(status) = config.result_cache.get(uuid.to_lowercase().as_str()) {
            let patch_status: PatchStatus = status.clone().into();
            debug!(
                "Found status {} for uuid: {} in cache",
                patch_status,
                uuid.to_lowercase()
            );
            patch_status
        } else {
            let status = match WindowsUpdate::new()
                .level(config.level + 1)
                .uuid(uuid.to_owned())
                .with_result_cache(config.result_cache.clone())
                .scanned_output(config.scanned_output.clone())
                .update_history(config.update_history.clone())
                .parse()
            {
                Ok(mut p) => {
                    p.evaluate_relationship_and_applicability_rules();
                    p.get_status()
                }
                Err(error) => {
                    error!(?error, "Failed to evaluate file with uuid {}", uuid);
                    match error {
                        WindowsXmlCheckerError::XmlParsingError(_) => PatchStatus::InvalidFile,
                        WindowsXmlCheckerError::FileNotFoundError(_) => {
                            PatchStatus::PreRequisiteFileMissing
                        }
                        _ => PatchStatus::Unknown,
                    }
                }
            };
            if config
                .result_cache
                .insert(uuid.to_lowercase(), status.to_owned().into())
                .is_none()
            {
                debug!(
                    "Added status {} for uuid: {} in cache",
                    status,
                    uuid.to_lowercase()
                );
            }
            status
        }
    }

    pub fn evaluate_prerequisites(&self, config: &RelationEvaluationConfig) -> PatchStatus {
        if self.prerequisites.as_ref().is_none() {
            debug!("No prerequisites found so considering installed");
            return PatchStatus::Installed;
        }
        let mut statuses = vec![];
        for prerequesites in self.prerequisites.as_ref().unwrap().into_iter() {
            match prerequesites {
                RelationshipItem::UpdateIdentity(update_identity) => {
                    let status = Relationships::evaluate_uuid(update_identity.get_uuid(), config);
                    debug!(
                        "L{} Response of uuid {} is {:?}",
                        config.level,
                        update_identity.get_uuid(),
                        status
                    );
                    statuses.push(status.clone());
                    if !status.is_applicable() {
                        debug!("Got patch status other than installed {} so skipping all further evaluation", status);
                        break;
                    }
                }
                RelationshipItem::AtLeastOne(at_least_one) => {
                    let mut is_atleast_one_installed = false;
                    for identity in &at_least_one.update_identities {
                        let status = Relationships::evaluate_uuid(identity.get_uuid(), config);
                        debug!(
                            "L{} Response of uuid {} is {:?}",
                            config.level,
                            identity.get_uuid(),
                            status
                        );
                        if status.is_applicable() {
                            is_atleast_one_installed = true;
                            break;
                        }
                    }

                    let relation_status = if is_atleast_one_installed {
                        PatchStatus::Installed
                    } else {
                        PatchStatus::NotReady
                    };
                    statuses.push(relation_status.clone());
                }
            }
        }
        debug!("============================ All prerequesites statuses at level {} are ===========================: {:?}", config.level, statuses);

        let status = if statuses
            .into_iter()
            .filter(|item| !item.is_applicable())
            .count()
            > 0
        {
            PatchStatus::NotReady
        } else {
            PatchStatus::Installed
        };
        debug!(
            "L{} is finished => Final Status of prerequesite is: {:?}",
            config.level, status
        );
        status
    }

    pub fn evaluate_bundled_updates(&self, config: &RelationEvaluationConfig) -> PatchStatus {
        if self.bundled_updates.as_ref().is_none() {
            debug!("No Bundled updates relationship found so considering installed");
            return PatchStatus::Installed;
        }
        let mut statuses = vec![];
        for bundled_update in self.bundled_updates.as_ref().unwrap().into_iter() {
            match bundled_update {
                RelationshipItem::UpdateIdentity(update_identity) => {
                    let status = Relationships::evaluate_uuid(update_identity.get_uuid(), config);
                    debug!(
                        "L{} Response of uuid {} is {:?}",
                        config.level,
                        update_identity.get_uuid(),
                        status
                    );
                    statuses.push(status.clone());
                    if !status.is_applicable() {
                        debug!("Got patch status other than installed {} so skipping all further evaluation", status);
                        break;
                    }
                }
                RelationshipItem::AtLeastOne(at_least_one) => {
                    let mut is_atleast_one_installed = false;
                    for identity in &at_least_one.update_identities {
                        let status = Relationships::evaluate_uuid(identity.get_uuid(), config);
                        debug!(
                            "L{} Response of uuid {} is {:?}",
                            config.level,
                            identity.get_uuid(),
                            status
                        );
                        if status.is_applicable() {
                            is_atleast_one_installed = true;
                            break;
                        }
                    }

                    let relation_status = if is_atleast_one_installed {
                        PatchStatus::Installed
                    } else {
                        PatchStatus::NotReady
                    };
                    statuses.push(relation_status.clone());
                }
            }
        }
        debug!("============================ All bundled updates statuses at level {} are ===========================: {:?}", config.level, statuses);

        let status = if statuses
            .into_iter()
            .filter(|item| !item.is_applicable())
            .count()
            > 0
        {
            PatchStatus::NotReady
        } else {
            PatchStatus::Installed
        };
        debug!(
            "L{} is finished => Final Status of bundled update is: {:?}",
            config.level, status
        );
        status
    }
}
