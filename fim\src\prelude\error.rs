use anyhow::Error as AnyhowError;
use notify_debouncer_full::notify::Error as NotifyError;
use thiserror::Error;
use tokio::task::JoinError;

#[derive(Error, Debug)]
pub enum FIMError {
    #[error("FIM Error: Failed to collect path from glob pattern {0:?}")]
    FailedToCollectPath(#[from] JoinError),

    #[error("FIM Error: Failed check config file {0:?}")]
    ConfigFileNotFound(String),

    #[error("FIM Error: Error occured in notify {0:?}")]
    NotifyError(#[from] NotifyError),

    #[error("FIM Error: Unknown error")]
    UnknownError(#[from] AnyhowError),
}
