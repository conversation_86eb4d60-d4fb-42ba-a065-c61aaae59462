use crate::{args::CmdArgs, prelude::EndpointopsError, run::init_api};
use endpointops_manager::EndpointOpsManager;
use logger::{error, info, ModuleLogger};
use std::{env, time::Duration};
use tokio::time::sleep;
use utils::shutdown::{get_shutdown_signal, start_shutdown_listener};

mod endpointops_manager;
mod upgrade;

pub use upgrade::*;

pub fn manager(args: &CmdArgs) -> Result<(), EndpointopsError> {
    let module_loger = ModuleLogger::new("global", None, Some("manager".to_owned()));

    let _guard = module_loger.guard();

    module_loger.set_global()?;

    info!("starting endpointops manager");
    info!("Agent Version: {}", env!("CARGO_PKG_VERSION"));
    info!(
        "Git Version: {} | {}",
        env!("GIT_BRANCH_OR_TAG"),
        env!("GIT_COMMIT_HASH")
    );

    std::panic::set_hook(Box::new(move |info| {
        let backtrace = backtrace::Backtrace::new();
        error!("Got a Panic With Message: {}", info.to_string());
        error!("Got a panic: {info:#?}\n");
        error!("Stack backtrace:\n{backtrace:?}");
    }));

    let rt = tokio::runtime::Builder::new_current_thread()
        .thread_name("endpointops-manager")
        .enable_all()
        .build();

    if let Err(error) = rt {
        error!(?error, "Failed to initialise async runtime");
        panic!("Failed to initialise async runtime");
    }

    let runtime = rt.unwrap();

    runtime.block_on(async move {
        start_shutdown_listener();

        let server_config = get_current_server_config(args).await?;
        // initialize api
        init_api(
            server_config.url(),
            server_config.username(),
            server_config.password(),
        )
        .await?;

        let mut manager = EndpointOpsManager::new(args);

        let cloned_args = args.clone();

        let mut upgrade_checker_handle = tokio::spawn(async move {
            let mut manager = EndpointOpsManager::new(&cloned_args);
            let mut shutdown_signal = get_shutdown_signal();
            loop {
                tokio::select! {
                    biased;

                    _ = shutdown_signal.recv() => {
                        break;
                    },

                    _ = sleep(Duration::from_secs(1 * 60 * 60)) => {
                        if let Err(error) = manager.check_for_upgrade(&server_config).await {
                            error!(?error, "Failed to handle upgrade flow");
                        }
                    }
                }
            }
        });

        if let Err(error) = manager.start_endpointops() {
            error!(?error, "Failed to start endpointops service");
            return Err(error);
        }

        let mut shutdown_handler = get_shutdown_signal();

        let handler = tokio::spawn(async move {
            tokio::select! {
                _ = &mut upgrade_checker_handle => {
                    Ok(())
                },

                _ = shutdown_handler.recv() => {
                    info!("Received shutdown signal.");
                    manager.stop_endpointops()
                }
            }
        });

        get_shutdown_signal().recv().await.ok();

        match handler.await {
            Ok(result) => {
                info!("Shutdown is complete! Good Bye!");
                result
            }
            Err(error) => {
                error!(?error, "Failed to join agent manager task");
                Err(error.into())
            }
        }
    })
}
