use crate::{args::CmdArgs, prelude::EndpointopsError, run::init_api};
use endpointops_manager::EndpointOpsManager;
use logger::{error, info, ModuleLogger};
use std::{env, time::Duration};
use tokio::{signal, sync::mpsc::Receiver, time::sleep};

mod endpointops_manager;
mod upgrade;

pub use upgrade::*;

pub fn manager(args: &CmdArgs, mut shutdown_signal: Receiver<()>) -> Result<(), EndpointopsError> {
    let module_loger = ModuleLogger::new("global", None, Some("manager".to_owned()));

    let _guard = module_loger.guard();

    module_loger.set_global()?;

    info!("starting endpointops manager");
    info!("Agent Version: {}", env!("CARGO_PKG_VERSION"));
    info!(
        "Git Version: {} | {}",
        env!("GIT_BRANCH_OR_TAG"),
        env!("GIT_COMMIT_HASH")
    );

    std::panic::set_hook(Box::new(move |info| {
        let backtrace = backtrace::Backtrace::new();
        error!("Got a Panic With Message: {}", info.to_string());
        error!("Got a panic: {info:#?}\n");
        error!("Stack backtrace:\n{backtrace:?}");
    }));

    let rt = tokio::runtime::Builder::new_current_thread()
        .thread_name("endpointops-manager")
        .enable_all()
        .build();

    if let Err(error) = rt {
        error!(?error, "Failed to initialise async runtime");
        panic!("Failed to initialise async runtime");
    }

    let runtime = rt.unwrap();

    runtime.block_on(async move {
        let server_config = get_current_server_config(args).await?;
        // initialize api
        init_api(
            server_config.url(),
            server_config.username(),
            server_config.password(),
        )
        .await?;

        let mut manager = EndpointOpsManager::new(args);

        let cloned_args = args.clone();

        let mut upgrade_checker_handle = tokio::spawn(async move {
            let mut manager = EndpointOpsManager::new(&cloned_args);
            loop {
                // check every 2 hour for upgrade
                sleep(Duration::from_secs(1 * 60 * 60)).await;
                if let Err(error) = manager.check_for_upgrade(&server_config).await {
                    error!(?error, "Failed to handle upgrade flow");
                }
            }
        });

        if let Err(error) = manager.start_endpointops() {
            error!(?error, "Failed to start endpointops service");
            return Err(error);
        }

        let result = tokio::select! {
            _ = &mut upgrade_checker_handle => {
                Ok(())
            },
            _ = shutdown_signal.recv() => {
                info!("Received shutdown signal.");
                manager.stop_endpointops()
            }
            _ = signal::ctrl_c() => {
                info!("Received Ctrl+C signal.");
                manager.stop_endpointops()
            }
        };
        info!("Exiting manager!");
        result
    })
}
