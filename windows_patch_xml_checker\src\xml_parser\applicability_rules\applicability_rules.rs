use super::Rules;
use crate::ParsedPackage;
use evalexpr::eval_boolean;
use logger::{debug, error};
use serde::Deserialize;
use std::sync::Arc;

pub struct ApplicabilityRulesEvaluationConfig {
    update_history: Arc<String>,
}

impl ApplicabilityRulesEvaluationConfig {
    pub fn new(update_history: Arc<String>) -> Self {
        Self { update_history }
    }

    #[allow(unused)]
    pub fn get_history(&self) -> &str {
        &self.update_history
    }
}

#[derive(Debug, Deserialize)]
pub struct InstalledRules {
    #[serde(rename = "$value")]
    rules: Vec<Rules>,
}

#[derive(Debug, Deserialize)]
pub struct InstallableRules {
    #[serde(rename = "$value")]
    rules: Vec<Rules>,
}

#[derive(Debug, Deserialize)]
pub struct SupersededRules {
    #[serde(rename = "$value")]
    rules: Vec<Rules>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct ApplicabilityRules {
    is_installed: Option<InstalledRules>,
    is_installable: Option<InstallableRules>,
    is_superseded: Option<SupersededRules>,
}

impl ApplicabilityRules {
    pub(crate) fn evaluate_rules(
        rules: &Vec<Rules>,
        package: &ParsedPackage,
        config: &ApplicabilityRulesEvaluationConfig,
    ) -> bool {
        let mut evaluation_results = vec![];
        for rule in rules {
            evaluation_results.push(rule.evaluate(package, config));
        }
        let experssion = evaluation_results
            .into_iter()
            .map(|item| if item { "true" } else { "false" })
            .collect::<Vec<&str>>()
            .join(" && ");
        debug!("Built expression {}", experssion);

        match eval_boolean(experssion.as_str()) {
            Ok(result) => result,
            Err(e) => {
                error!("Failed to evaluate expression {}, {:?}", experssion, e);
                false
            }
        }
    }

    pub fn evaluate_applicability_rules(
        &self,
        package: &ParsedPackage,
        config: &ApplicabilityRulesEvaluationConfig,
    ) -> ApplicabilityRulesEvaluationResult {
        let mut is_installed: Option<bool> = None;
        let mut is_installable: Option<bool> = None;

        if let Some(superseeded_rules) = self.is_superseded.as_ref() {
            debug!(
                "=============================================Superseded============================================="
            );
            let result =
                ApplicabilityRules::evaluate_rules(&superseeded_rules.rules, package, config);
            debug!("Result of is_superseded rules is {}", result);
            if result {
                is_installed = Some(true)
            }
        }
        if let Some(installed_rules) = self.is_installed.as_ref() {
            debug!(
                "=============================================IsInstalled============================================="
            );
            let result =
                ApplicabilityRules::evaluate_rules(&installed_rules.rules, package, config);
            debug!("Result of is_installed rules is {}", result);
            is_installed = Some(result);
        }
        if let Some(installable_rules) = self.is_installable.as_ref() {
            debug!(
                "=============================================IsInstallable============================================="
            );
            let result =
                ApplicabilityRules::evaluate_rules(&installable_rules.rules, package, config);
            debug!("Result of is_installable rules is {}", result);
            is_installable = Some(result);
        }

        ApplicabilityRulesEvaluationResult {
            is_installed,
            is_installable,
        }
    }
}

#[derive(Debug, Default)]
pub struct ApplicabilityRulesEvaluationResult {
    pub is_installed: Option<bool>,
    pub is_installable: Option<bool>,
}
