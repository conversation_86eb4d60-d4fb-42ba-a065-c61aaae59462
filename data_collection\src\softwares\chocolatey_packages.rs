use super::software::{PackageType, Software};
use glob::glob;
use logger::{error, info};
use serde::Deserialize;
use serde_json::json;
use serde_xml_rs::{Deserializer, EventReader, ParserConfig};
use std::{collections::HashSet, fs::File, path::PathBuf};
use utils::shutdown::is_system_running;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
struct PackageMetadata {
    id: Option<String>,
    version: Option<String>,
    summary: Option<String>,
    authors: Option<String>,
    license_url: Option<String>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
struct ChocoXmlPackage {
    metadata: PackageMetadata,
}

impl From<ChocoXmlPackage> for ChocolateyPackage {
    fn from(value: ChocoXmlPackage) -> Self {
        let pkg_metadata = value.metadata;
        Self {
            name: pkg_metadata.id.unwrap_or_default(),
            path: "".to_string(),
            version: pkg_metadata.version.unwrap_or_default(),
            summary: pkg_metadata.summary.unwrap_or_default(),
            author: pkg_metadata.authors.unwrap_or_default(),
            license: pkg_metadata.license_url.unwrap_or_default(),
        }
    }
}

#[derive(Debug, PartialEq, Eq, Hash, Default)]
pub struct ChocolateyPackage {
    name: String,
    path: String,
    version: String,
    summary: String,
    author: String,
    license: String,
}

impl From<ChocolateyPackage> for Software {
    fn from(value: ChocolateyPackage) -> Self {
        Software {
            name: value.name,
            version: value.version,
            vendor: value.author,
            r#type: PackageType::ChocolateyPackages,
            properties: json!({
                "path": value.path,
                "summary": value.summary,
                "license": value.license
            }),
            ..Default::default()
        }
    }
}

impl ChocolateyPackage {
    fn generate_package(file_path: &PathBuf) -> Option<ChocolateyPackage> {
        let config = ParserConfig::new()
            .trim_whitespace(true)
            .whitespace_to_characters(false)
            .ignore_comments(true);

        let event_reader = match File::open(file_path) {
            Ok(file) => EventReader::new_with_config(file, config),
            Err(error) => {
                error!(?error, "Failed to open file {}", file_path.display());
                return None;
            }
        };

        let pkg = match ChocoXmlPackage::deserialize(&mut Deserializer::new(event_reader)) {
            Ok(package) => package,
            Err(error) => {
                error!(?error, "Failed to parse package {}", file_path.display());
                return None;
            }
        };
        let mut choco_pkg: ChocolateyPackage = pkg.into();
        choco_pkg.path = file_path.to_string_lossy().to_string();
        Some(choco_pkg)
    }

    pub fn collect() -> HashSet<Software> {
        let chocolatey_path = match std::env::var("ChocolateyInstall") {
            Ok(value) => PathBuf::from(value),
            Err(_) => {
                info!("No chocolatey installation found");
                return HashSet::new();
            }
        };

        let glob_pattern = chocolatey_path
            .join("lib/*/*.nuspec")
            .to_string_lossy()
            .to_string();

        let paths = match glob(&glob_pattern) {
            Ok(paths) => paths.into_iter().filter_map(Result::ok),
            Err(error) => {
                error!(?error, "Failed to read glob pattern {}", glob_pattern);
                return HashSet::new();
            }
        };

        paths
            .take_while(|_| is_system_running())
            .map(|path| ChocolateyPackage::generate_package(&path))
            .filter(|item| item.is_some())
            .map(|item| item.unwrap().into())
            .collect()
    }
}
