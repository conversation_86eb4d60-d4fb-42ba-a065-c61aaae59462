use logger::{debug, error};

#[cfg(windows)]
pub fn parse_cmd(input: &str) -> (String, Vec<String>) {
    use windows::{
        core::PCWSTR,
        Win32::{
            Foundation::{LocalFree, HLOCAL},
            UI::Shell::CommandLineToArgvW,
        },
    };

    let wide: Vec<u16> = input.encode_utf16().chain(std::iter::once(0)).collect();
    let mut argc = 0;
    let argv_ptr = unsafe { CommandLineToArgvW(PCWSTR(wide.as_ptr()), &mut argc) };

    if argv_ptr.is_null() || argc == 0 {
        error!(
            "Failed to parse arguments of windows CommandLinetoArgvW {}",
            input
        );
        return (input.to_owned(), vec![]);
    }

    let args = unsafe {
        std::slice::from_raw_parts(argv_ptr, argc as usize)
            .iter()
            .map(|&arg| {
                let len = (0..).take_while(|&i| *arg.0.offset(i) != 0).count();
                let slice = std::slice::from_raw_parts(arg.0, len);
                String::from_utf16_lossy(slice)
            })
            .collect::<Vec<String>>()
    };

    unsafe { LocalFree(Some(HLOCAL(argv_ptr as _))) };

    let command = args[0].to_owned();
    let args = args[1..]
        .into_iter()
        .map(|i| i.to_owned())
        .collect::<Vec<String>>();
    debug!("Parsed command {} and args {:?}", command, args);
    (command, args)
}

#[cfg(unix)]
pub fn parse_cmd(input: &str) -> (String, Vec<String>) {
    match shell_words::split(input) {
        Ok(parts) if !parts.is_empty() => {
            let command = parts[0].to_owned();
            let args = parts[1..]
                .into_iter()
                .map(|i| i.to_owned())
                .collect::<Vec<String>>();
            debug!("Parsed command {} and args {:?}", command, args);
            (command, args)
        }
        _ => {
            error!("Failed to parse args using shell words {}", input);
            (input.to_owned(), vec![])
        }
    }
}
