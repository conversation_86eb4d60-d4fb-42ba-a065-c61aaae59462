use data_collection::{
    Certificates, DataCollectionExtension, NetworkInterfaces, OsServices, Processes, Provision,
    Resources, Softwares, StartUpItems, Users,
};
use database::{
    models::{AgentConfig, AgentMetadata},
    Database, DbOptions,
};
use logger::ModuleLogger;
use std::sync::RwLock;
use tokio::fs;
use utils::{
    dir::{get_db_dir, get_log_dir},
    GLOBAL_LOG_LEVEL,
};

#[tokio::main]
async fn main() {
    GLOBAL_LOG_LEVEL.get_or_init(|| RwLock::new("debug".to_owned()));
    Database::init(DbOptions::new(
        get_db_dir(),
        "endpointops",
        "endpointops",
        "endpointops",
        "endpointops_ziro@ziro@2019",
    ))
    .await
    .unwrap();
    // console_subscriber::init();
    let module_loger = ModuleLogger::new("global", None, Some("endpointops".to_owned()));

    // console_subscriber::init();

    let _guard = module_loger.guard();

    module_loger.set_global().unwrap();

    let agent_metadata = AgentMetadata::new(6, AgentConfig::default());

    let mut provision_data = Provision::new("uuid".to_owned(), "enroll_secret".to_owned());
    provision_data.collect().await.unwrap();
    fs::write(
        get_log_dir().join("provision.json"),
        serde_json::to_string_pretty(&provision_data.get_data()).unwrap(),
    )
    .await
    .unwrap();

    let mut resources = Resources::new(&agent_metadata);
    resources.collect().await.unwrap();
    fs::write(
        get_log_dir().join("resources.json"),
        serde_json::to_string_pretty(&resources.build_payload().unwrap()).unwrap(),
    )
    .await
    .unwrap();

    let mut processes = Processes::new(&agent_metadata);
    processes.collect().await.unwrap();
    fs::write(
        get_log_dir().join("processes.json"),
        serde_json::to_string_pretty(&processes.build_payload().unwrap()).unwrap(),
    )
    .await
    .unwrap();

    let mut users = Users::new(&agent_metadata);
    users.collect().await.unwrap();
    fs::write(
        get_log_dir().join("users.json"),
        serde_json::to_string_pretty(&users.build_payload().unwrap()).unwrap(),
    )
    .await
    .unwrap();

    let mut startup = StartUpItems::new(&agent_metadata);
    startup.collect().await.unwrap();
    fs::write(
        get_log_dir().join("startup.json"),
        serde_json::to_string_pretty(&startup.build_payload().unwrap()).unwrap(),
    )
    .await
    .unwrap();

    let mut network_interfaces = NetworkInterfaces::new(&agent_metadata);
    network_interfaces.collect().await.unwrap();
    fs::write(
        get_log_dir().join("network_interfaces.json"),
        serde_json::to_string_pretty(&network_interfaces.build_payload().unwrap()).unwrap(),
    )
    .await
    .unwrap();

    let mut certificates = Certificates::new(&agent_metadata);
    certificates.collect().await.unwrap();
    fs::write(
        get_log_dir().join("certificates.json"),
        serde_json::to_string_pretty(&certificates.build_payload().unwrap()).unwrap(),
    )
    .await
    .unwrap();

    let mut softwares = Softwares::new(&agent_metadata);
    softwares.collect().await.unwrap();
    fs::write(
        get_log_dir().join("softwares.json"),
        serde_json::to_string_pretty(&softwares.build_payload().unwrap()).unwrap(),
    )
    .await
    .unwrap();

    let mut services = OsServices::new(&agent_metadata);
    services.collect().await.unwrap();
    fs::write(
        get_log_dir().join("os_services.json"),
        serde_json::to_string_pretty(&services.build_payload().unwrap()).unwrap(),
    )
    .await
    .unwrap();
}
