use crate::WMIGetter;
use logger::{debug, error};
use serde::Deserialize;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct WmiQuery {
    namespace: Option<String>,
    wql_query: String,
}

impl WmiQuery {
    pub fn evaluate(&self) -> bool {
        debug!("Inspecting WMI Query rule");
        match WMIGetter::new(self.namespace.to_owned()) {
            Ok(connection) => {
                let result = connection.query(&self.wql_query);
                debug!("Got result of wmi query {:?}", result);
                result.len() > 0
            }
            Err(error) => {
                error!(?error, "Failed to build wmi connection with {:?}", self);
                false
            }
        }
    }
}
