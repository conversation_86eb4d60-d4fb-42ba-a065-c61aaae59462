use std::{
    env, fs,
    path::{Path, PathBuf},
    process::Command,
};

use build_target::Os;

fn main() {
    // Get current Git commit hash
    let commit = Command::new("git")
        .args(["rev-parse", "HEAD"])
        .output()
        .map(|o| String::from_utf8_lossy(&o.stdout).trim().to_string())
        .unwrap_or_else(|_| "unknown".into());

    // Get current branch or tag name
    let describe = Command::new("git")
        .args(["describe", "--tags", "--always", "--dirty"])
        .output()
        .map(|o| String::from_utf8_lossy(&o.stdout).trim().to_string())
        .unwrap_or_else(|_| "unknown".into());

    println!("cargo:rustc-env=GIT_COMMIT_HASH={}", commit);
    println!("cargo:rustc-env=GIT_BRANCH_OR_TAG={}", describe);

    let target_arch = match build_target::target_arch().unwrap().as_str() {
        "aarch64" => "arm64",
        "arm64" => "arm64",
        _ => "x64",
    };

    let target_platform = match build_target::target_os().unwrap() {
        Os::Windows => "Windows",
        Os::Linux => "Linux",
        Os::MacOs => "Mac",
        _ => "Unsupported",
    };

    let version = env::var("CARGO_PKG_VERSION").unwrap();

    let out_dir = env::var("OUT_DIR").unwrap();
    let profile = env::var("PROFILE").unwrap(); // "debug" or "release"

    // Traverse up from OUT_DIR until we find `target/` root
    let target_dir = find_target_dir(PathBuf::from(&out_dir).as_path(), &profile)
        .expect("Could not find target dir");

    let dest_path = Path::new(&target_dir).join("build.txt");
    fs::write(
        &dest_path,
        format!(
            "version={}\nplatform={}\narch={}",
            version, target_platform, target_arch
        ),
    )
    .unwrap();

    #[cfg(windows)]
    {
        use std::{env, fs, path::Path};

        let version = env::var("CARGO_PKG_VERSION").unwrap();
        let version_parts: Vec<&str> = version.split('.').collect();
        let (major, minor, patch, build) = (
            version_parts.get(0).unwrap_or(&"0"),
            version_parts.get(1).unwrap_or(&"0"),
            version_parts.get(2).unwrap_or(&"0"),
            "0", // Optional build number
        );

        let rc_content = format!(
            r#"#include "winuser.h"

1 ICON "zirozen.ico"
1 RT_MANIFEST "endpointops.manifest"
1 VERSIONINFO
FILEVERSION     {0},{1},{2},{3}
PRODUCTVERSION  {0},{1},{2},{3}
FILEFLAGSMASK   0x3fL
FILEFLAGS       0x0L
FILEOS          0x40004L
FILETYPE        0x1L
FILESUBTYPE     0x0L

BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName",      "Zirozen Software Corporation LLP\0"
            VALUE "FileDescription",  "EndpointOps\0"
            VALUE "FileVersion",      "{0}.{1}.{2}.{3}\0"
            VALUE "InternalName",     "endpointops.exe\0"
            VALUE "OriginalFilename", "endpointops.exe\0"
            VALUE "ProductName",      "EndpointOps\0"
            VALUE "ProductVersion",   "{0}.{1}.{2}.{3}\0"
            VALUE "LegalCopyright",   "Copyright 2025 Zirozen Software Corporation LLP. All Rights reserved.\0"
        END
    END

    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x0409, 1200
    END
END
"#,
            major, minor, patch, build
        );

        let rc_path = Path::new(&out_dir).join("app.rc");
        fs::write(&rc_path, rc_content).unwrap();

        let _ = embed_resource::compile(rc_path, embed_resource::NONE).manifest_required();

        println!("cargo:rerun-if-changed=app.rc");
    }

    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=.git/HEAD");
}

fn find_target_dir(out_dir: &Path, profile: &str) -> Option<PathBuf> {
    // Go up until we find a parent ending with "target/{profile}"
    let mut current = Some(out_dir);

    while let Some(path) = current {
        if path.ends_with(format!("target/{profile}")) || path.ends_with(profile) {
            return Some(path.to_path_buf());
        }
        current = path.parent();
    }
    None
}
