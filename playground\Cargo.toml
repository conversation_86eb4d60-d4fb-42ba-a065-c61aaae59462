[package]
name = "playground"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
console-subscriber = "0.4.1"
database = { path = "../database" }
fim = { path = "../fim" }
data_collection = { path = "../data_collection" }
logger = { path = "../logger" }
agents = { path = "../agents" }
# windows_patch_xml_checker = { path = "../windows_patch_xml_checker" }
utils = { path = "../utils" }
tokio = { version = "1.43.0", features = ["full", "tracing"] }
encoding_rs_io = "0.1.7"
encoding_rs = "0.8.35"
serde_json = "1.0.138"
glob = "0.3.2"
rayon = "1.10.0"
walkdir = "2.5.0"
ignore = "0.4.23"
globset = "0.4.15"
sysinfo = "0.33.1"
