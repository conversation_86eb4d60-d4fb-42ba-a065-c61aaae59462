use logger::{debug, error};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashSet,
    fmt::Debug,
    sync::{Arc, RwLock},
};

use super::{FIMConfig, QuickCheck, SoftwareMeterRule};

#[derive(Debug, Default, Serialize, Deserialize, <PERSON>lone)]
pub struct AgentRefreshTimeSettings {
    pub refresh_cycle: u64,
    pub system_action_refresh_cycle: u64,
    pub patch_refresh_cycle: u64,
    pub sbom_refresh_cycle: u64,
    pub process_refresh_cycle: u64,
    pub network_refresh_cycle: u64,
    pub certificate_refresh_cycle: u64,
    pub startup_items_refresh_cycle: u64,
    pub users_refresh_cycle: u64,
    pub resources_refresh_cycle: u64,
    pub file_events_refresh_cycle: u64,
    pub service_refresh_cycle: u64,
    pub quick_check_refresh_cycle: u64,
    pub software_meter_refresh_cycle: u64,
    pub max_file_download_speed: Option<usize>,
}

#[derive(Debug, Default, Serialize, Deserialize, <PERSON><PERSON>)]
pub struct AgentConfig {
    #[serde(alias = "agent_settings")]
    pub agent_refresh_time_settings: AgentRefreshTimeSettings,
    #[serde(alias = "fim")]
    pub fim_config: Option<HashSet<FIMConfig>>,
    pub quick_checks: Option<HashSet<QuickCheck>>,
    #[serde(alias = "software_meter")]
    pub software_meter_cofigurations: Option<HashSet<SoftwareMeterRule>>,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct AgentMetadata {
    id: i64,
    config: Arc<RwLock<AgentConfig>>,
}

impl Debug for AgentMetadata {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("AgentMetadata")
            .field("id", &self.id)
            .field("config", &self.config)
            .finish()
    }
}

impl AgentMetadata {
    pub fn new(id: i64, config: AgentConfig) -> Self {
        Self {
            id,
            config: Arc::new(RwLock::new(config)),
        }
    }

    pub fn get_endpoint_id(&self) -> i64 {
        self.id.clone().into()
    }

    pub fn get_config(&self) -> AgentConfig {
        if let Ok(config) = self.config.read() {
            config.clone()
        } else {
            error!("Unable to aquire lock on the agent metadata config");
            AgentConfig::default()
        }
    }

    pub fn get_agent_refresh_settings(&self) -> AgentRefreshTimeSettings {
        if let Ok(config) = self.config.read() {
            config.clone().agent_refresh_time_settings
        } else {
            error!("Unable to aquire lock on the agent metadata config");
            AgentRefreshTimeSettings::default()
        }
    }

    pub fn update_config(&self, updated_config: AgentConfig) {
        debug!(
            "Agent configuration has been updated new config {:?}",
            updated_config
        );

        let config = self.config.write();

        if config.is_err() {
            error!(
                "Failed to aquire lock on agent metadata config {:?}",
                config.err().unwrap()
            );
            return;
        }

        if let Ok(mut config) = config {
            *config = updated_config;
        }

        debug!("Updated Agent metadata config");
    }
}
