use chrono::{DateTime, Utc};
use database::models::FIMConfig;
use notify_debouncer_full::notify::Event;
use std::time::SystemTime;

use crate::FIMEvent;

#[derive(Debug, Clone)]
pub struct SystemTimedEvent<'a> {
    timestamp: SystemTime,
    event: Event,
    config: &'a FIMConfig,
}

impl<'a> SystemTimedEvent<'a> {
    pub fn new(event: Event, config: &'a FIMConfig) -> Self {
        Self {
            timestamp: SystemTime::now(),
            event,
            config,
        }
    }

    pub fn build_fim_event(self) -> FIMEvent {
        let mut fs_event = FIMEvent::build_from(self.event, self.config);
        let event_time: DateTime<Utc> = self.timestamp.into();
        fs_event.generate_id();
        fs_event.set_event_time(event_time.timestamp());
        fs_event
    }
}
