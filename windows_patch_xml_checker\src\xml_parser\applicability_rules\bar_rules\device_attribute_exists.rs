use logger::debug;
use serde::Deserialize;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct DeviceAttributeExists {
    name: Option<String>,
}

impl DeviceAttributeExists {
    pub fn evaluate(&self) -> bool {
        debug!("Inspecting Device Attribute Exist rule");

        debug!("Name: {}", self.name.as_ref().unwrap());
        // @TODO implement this rule
        false
    }
}
