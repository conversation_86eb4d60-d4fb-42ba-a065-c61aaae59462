use super::provision_data::ProvisionData;
use crate::{DataCollectionError, DataCollectionExtension};
use api::agent::provision_agent;
use async_trait::async_trait;
use logger::{debug, error};
use serde_json::Value;
use std::time::Instant;

#[derive(Debug)]
pub struct Provision {
    data: ProvisionData,
    enroll_secret: String,
    uuid: String,
    endpoint_id: i64,
}

impl Provision {
    pub fn new(uuid: String, enroll_secret: String) -> Self {
        Self {
            data: Default::default(),
            enroll_secret,
            uuid,
            endpoint_id: 0,
        }
    }

    pub fn get_data(&self) -> &ProvisionData {
        &self.data
    }

    pub fn get_endpoint_id(&self) -> i64 {
        self.endpoint_id
    }
}

#[async_trait]
impl DataCollectionExtension for Provision {
    fn get_refresh_interval(&self) -> u64 {
        0
    }

    fn get_name(&self) -> &str {
        "provision_ext"
    }

    fn get_endpoint_id(&self) -> i64 {
        0
    }

    fn build_payload(&self) -> Result<Value, DataCollectionError> {
        Ok(serde_json::to_value(&self.data)?)
    }

    async fn send(&mut self) -> Result<(), DataCollectionError> {
        let endpoint_id = provision_agent(self.build_payload()?).await?;
        self.endpoint_id = endpoint_id.as_i64().unwrap_or_default();
        Ok(())
    }

    async fn collect(&mut self) -> Result<(), DataCollectionError> {
        let logger = self.logger();
        let uuid = self.uuid.to_owned();
        let enroll_secret = self.enroll_secret.to_owned();
        match tokio::task::Builder::new()
            .name(self.get_name())
            .spawn_blocking(move || {
                logger.with(|| {
                    let time = Instant::now();
                    let collection = ProvisionData::collect(uuid, enroll_secret);
                    debug!("Time taken for collection {:?}", time.elapsed());
                    collection
                })
            })
            .unwrap()
            .await
        {
            Ok(data) => {
                self.data = data;
                Ok(())
            }
            Err(error) => {
                error!(?error, "Failed to join task for {}", self.get_name());
                Err(error.into())
            }
        }
    }
}
