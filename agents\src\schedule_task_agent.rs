use agent_manager::Agent<PERSON><PERSON>na<PERSON>;
use anyhow::Result;
use api::task::change_task_only_status;
use async_trait::async_trait;
use database::is_system_rebooted_recently;
use database::models::{AgentMetadata, Task, TaskStatus};
use logger::info;
use logger::{debug, error};
use std::time::Duration;
use task_execution::build_executable_task;
use tokio::select;
use tokio::sync::mpsc::UnboundedSender;
use tokio::time::interval;
use utils::shutdown::get_shutdown_signal;

pub struct ScheduledTaskAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    task_sender: UnboundedSender<Task>,
}

impl<'a> ScheduledTaskAgent<'a> {
    pub fn new(
        agent_metadata: &'a AgentMetadata,
        task_sender: UnboundedSender<Task>,
    ) -> ScheduledTaskAgent<'a> {
        ScheduledTaskAgent {
            agent_metadata,
            task_sender,
        }
    }
}

#[async_trait]
impl AgentRunnable for ScheduledTaskAgent<'static> {
    fn get_name(&self) -> &str {
        "scheduled_task_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting Scheduled Task Agent ------------------------");

        let mut shutdown_signal = get_shutdown_signal();
        let mut interval = interval(Duration::from_secs(60));

        // check if system is recently booted and task with success with reboot is in db
        if is_system_rebooted_recently() {
            let tasks = Task::get_reboot_required_tasks().await;
            if tasks.len() > 0 {
                info!(
                    "Selected total {} tasks with success reboot status to be marked as success",
                    tasks.len()
                );

                for task in tasks {
                    let task_id = task.id.to_i64();
                    match build_executable_task(task, self.agent_metadata.clone()) {
                        Ok(executable) => {
                            if change_task_only_status(vec![task_id], TaskStatus::Success)
                                .await
                                .is_ok()
                            {
                                if let Err(error) = executable.remove_task_resources().await {
                                    error!(
                                        "Failed to remove task resources for task {} with error {:?}",
                                        self.get_name(),
                                        error
                                    );
                                }
                            }
                            drop(executable);
                        }
                        Err(error) => {
                            error!(?error, "Failed to build task executable from message");
                        }
                    };
                }
            }
        }

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down Scheduled Task Agent");
                    break;
                }

                _ = interval.tick() => {
                    debug!("Checking for scheduled tasks");
                    let tasks: Vec<Task> = Task::get_pending_tasks().await;
                    if tasks.len() > 0 {
                        info!("Selected total {} tasks", tasks.len());

                        for task in tasks.into_iter() {
                            debug!("Processing task {:?}", task);
                            if let Err(error) = self.task_sender.send(task) {
                                error!(?error, "Failed to send task to task receiver {:?}", error.0);
                            }
                        }
                    }
                },
            }
        }
        info!("---------------------- Stopped Scheduled Task Agent ------------------------");
        Ok(())
    }
}
