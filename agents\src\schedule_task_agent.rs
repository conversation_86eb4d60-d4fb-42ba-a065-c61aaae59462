use agent_manager::Agent<PERSON><PERSON><PERSON><PERSON>;
use anyhow::Result;
use api::task::change_task_only_status;
use async_trait::async_trait;
use database::is_system_rebooted_recently;
use database::models::{AgentMetadata, Task, TaskStatus};
use logger::info;
use logger::{debug, error};
use std::sync::Arc;
use std::time::Duration;
use task_execution::build_executable_task;
use tokio::select;
use tokio::sync::mpsc::{channel, Receiver, Sender, UnboundedSender};
use tokio::sync::Mutex;
use tokio::time::interval;

pub struct ScheduledTaskAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    task_sender: UnboundedSender<Task>,
    stop_signal_sender: Sender<bool>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl<'a> ScheduledTaskAgent<'a> {
    pub fn new(
        agent_metadata: &'a AgentMetadata,
        task_sender: UnboundedSender<Task>,
    ) -> ScheduledTaskAgent<'a> {
        let (stop_signal_sender, stop_signal_receiver) = channel(1);

        ScheduledTaskAgent {
            agent_metadata,
            task_sender,
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            stop_signal_sender,
        }
    }
}

#[async_trait]
impl AgentRunnable for ScheduledTaskAgent<'static> {
    fn get_name(&self) -> &str {
        "scheduled_task_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting Scheduled Task Agent ------------------------");
        let receiver_arc = self.stop_signal_receiver.clone();

        let mut receiver = receiver_arc.lock().await;

        let mut interval = interval(Duration::from_secs(60));

        // check if system is recently booted and task with success with reboot is in db
        if is_system_rebooted_recently() {
            let tasks = Task::get_reboot_required_tasks().await;
            if tasks.len() > 0 {
                info!(
                    "Selected total {} tasks with success reboot status to be marked as success",
                    tasks.len()
                );

                for task in tasks {
                    let task_id = task.id.to_i64();
                    match build_executable_task(task, self.agent_metadata.clone()) {
                        Ok(executable) => {
                            if change_task_only_status(vec![task_id], TaskStatus::Success)
                                .await
                                .is_ok()
                            {
                                if let Err(error) = executable.remove_task_resources().await {
                                    error!(
                                        "Failed to remove task resources for task {} with error {:?}",
                                        self.get_name(),
                                        error
                                    );
                                }
                            }
                            drop(executable);
                        }
                        Err(error) => {
                            error!(?error, "Failed to build task executable from message");
                        }
                    };
                }
            }
        }

        loop {
            select! {
                biased;

                _ = receiver.recv() => {
                    info!("Shutting Down Scheduled Task Agent");
                    break;
                }

                _ = interval.tick() => {
                    debug!("Checking for scheduled tasks");
                    let tasks: Vec<Task> = Task::get_pending_tasks().await;
                    if tasks.len() > 0 {
                        info!("Selected total {} tasks", tasks.len());

                        for task in tasks.into_iter() {
                            debug!("Processing task {:?}", task);
                            if let Err(error) = self.task_sender.send(task) {
                                error!(?error, "Failed to send task to task receiver {:?}", error.0);
                            }
                        }
                    }
                },
            }
        }
        info!("---------------------- Stopped Scheduled Task Agent ------------------------");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if let Err(error) = self.stop_signal_sender.send(true).await {
            error!(?error, "Failed to send stop signal to scheduled task agent");
        };
        Ok(())
    }

    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
}
