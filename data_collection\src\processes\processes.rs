use super::{listening_port::ListeningPort, process::Process, remote_connection::RemoteConnection};
use crate::{DataCollectionError, DataCollectionExtension};
use anyhow::Result;
use async_trait::async_trait;
use database::models::AgentMetadata;
use logger::debug;
use serde_json::{json, Value};
use std::{collections::HashSet, time::Instant};
use sysinfo::{ProcessRefreshKind, RefreshKind, System};

#[derive(Debug)]
pub struct Processes<'a> {
    processes: HashSet<Process>,
    listening_ports: HashSet<ListeningPort>,
    remote_connections: HashSet<RemoteConnection>,
    agent_metadata: &'a AgentMetadata,
}

impl<'a> Processes<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> Self {
        Self {
            agent_metadata,
            listening_ports: HashSet::new(),
            remote_connections: HashSet::new(),
            processes: HashSet::new(),
        }
    }
}

#[async_trait]
impl<'a> DataCollectionExtension for Processes<'a> {
    fn get_refresh_interval(&self) -> u64 {
        self.agent_metadata
            .get_agent_refresh_settings()
            .process_refresh_cycle
    }

    fn get_name(&self) -> &str {
        "process_ext"
    }

    fn get_endpoint_id(&self) -> i64 {
        self.agent_metadata.get_endpoint_id()
    }

    fn build_payload(&self) -> Result<Value, DataCollectionError> {
        Ok(json!({
            "asset_id" : self.get_endpoint_id(),
            "data" : json!({
                "process_details": self.processes,
                "listening_port_details": self.listening_ports,
                "remote_connections_details": self.remote_connections
            })
        }))
    }

    fn collect(&mut self) -> Result<(), DataCollectionError> {
        let data = self.logger().with(|| {
            let time = Instant::now();
            let mut sys = System::new_with_specifics(
                RefreshKind::nothing().with_processes(ProcessRefreshKind::everything()),
            );
            let processes = Process::collect(&mut sys);
            let listening_ports = ListeningPort::collect(&sys);
            let remote_connections = RemoteConnection::collect(&sys);
            debug!("Time taken for collection {:?}", time.elapsed());
            (processes, listening_ports, remote_connections)
        });

        self.processes = data.0;
        self.listening_ports = data.1;
        self.remote_connections = data.2;
        Ok(())
    }
}
