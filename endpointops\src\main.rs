mod args;
mod install;
mod manager;
pub mod prelude;
mod run;
mod uninstall;
mod wol;

#[cfg(windows)]
mod service;

use anyhow::Result;
use args::CmdArgs;
use clap::Parser;
use database::{
    models::ServerConfig, Database, DbOptions, Model, PrimaryKey, DB_NAME, DB_NAME_SPACE,
    DB_PASSWORD, DB_USERNAME,
};
use install::{build_server_config, install, verify_server_config};
use logger::error;
use manager::manager;
use prelude::EndpointopsError;
use run::run;
use std::{path::PathBuf, sync::RwLock};
use tokio::runtime::Builder;
use uninstall::uninstall;
use utils::{dir::get_current_dir, GLOBAL_LOG_LEVEL};

use crate::wol::wol;

// #[cfg(not(windows))]
fn main() -> Result<(), EndpointopsError> {
    let mut args = CmdArgs::parse();

    if args.version {
        println!("Agent Version: {}", env!("CARGO_PKG_VERSION"));
        println!(
            "Git Version: {} | {}",
            env!("GIT_BRANCH_OR_TAG"),
            env!("GIT_COMMIT_HASH")
        );
        return Ok(());
    }

    let log = args.log.clone();
    GLOBAL_LOG_LEVEL.get_or_init(|| RwLock::new(log));

    /*
     * if given remote url and enroll_id as parameters, we will use that and persist it.
     */
    if args.remote_url.is_some() && args.enroll_id.is_some() {
        let rt = Builder::new_multi_thread().enable_all().build();
        args.install_path = get_current_dir().to_string_lossy().to_string();

        if let Err(error) = rt {
            error!(?error, "Failed to initialise async runtime");
            panic!("Failed to initialise async runtime");
        }

        let runtime = rt.unwrap();

        let args_clone = args.clone();

        runtime.block_on(async move {
            let db = Database::disposable_connection(DbOptions::new(
                PathBuf::from(&args_clone.install_path)
                    .join("data")
                    .as_path(),
                DB_NAME_SPACE,
                DB_NAME,
                DB_USERNAME,
                DB_PASSWORD,
            ))
            .await?;

            let server_config = ServerConfig::default()
                .set_id(PrimaryKey::ZirozenId(1))
                .load_with_connection(db)
                .await;

            // server configs are already available
            if server_config.is_ok() {
                return Ok::<(), EndpointopsError>(());
            }

            verify_server_config(
                build_server_config(
                    args_clone.remote_url.as_ref().unwrap().to_owned(),
                    args_clone.enroll_id.as_ref().unwrap().to_owned(),
                    args_clone.uuid.to_owned(),
                )
                .await?,
                &args_clone,
            )
            .await?;
            Ok::<(), EndpointopsError>(())
        })?;
    }

    if args.wol {
        wol(&args)
    } else if args.agent {
        if args.foreground {
            run(&args)
        } else {
            #[cfg(windows)]
            {
                Ok(service::start()?)
            }
            #[cfg(not(windows))]
            {
                run(&args)
            }
        }
    } else if args.manager {
        if args.foreground {
            manager(&args)
        } else {
            #[cfg(windows)]
            {
                Ok(service::start()?)
            }
            #[cfg(not(windows))]
            {
                manager(&args)
            }
        }
    } else if args.uninstall {
        uninstall(&args)
    } else {
        install(&args)
    }
}
