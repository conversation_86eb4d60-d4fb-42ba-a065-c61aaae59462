use agent_manager::Agent<PERSON><PERSON><PERSON><PERSON>;
use anyhow::Result;
use api::data_collection::send_fim_data;
use async_trait::async_trait;
use database::{models::AgentMetadata, Model};
use fim::FIMEvent;
use logger::info;
use logger::{debug, error};
use serde_json::json;
use std::sync::Arc;
use std::time::Duration;
use tokio::select;
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::sync::Mutex;
use tokio::time::sleep;

pub struct FIMDataSyncAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    stop_signal_sender: Sender<bool>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl<'a> FIMDataSyncAgent<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> FIMDataSyncAgent<'a> {
        let (stop_signal_sender, stop_signal_receiver) = channel(1);

        FIMDataSyncAgent {
            agent_metadata,
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            stop_signal_sender,
        }
    }

    async fn send_all_pending_events(agent_metadata: &AgentMetadata) {
        loop {
            match FIMEvent::default().get_all(Some(100)).await {
                Ok(items) => {
                    let found_items = items.len();
                    if found_items > 0 {
                        if let Err(error) = send_fim_data(json!({
                                "asset_id" : agent_metadata.get_endpoint_id(),
                                "data" : json!({
                                    "fim_events": items,
                                })
                        }))
                        .await
                        {
                            error!(
                                ?error,
                                "Failed to send {} events to server and will be added back to",
                                found_items
                            );
                        } else {
                            match FIMEvent::default()
                                .delete_selected(items.into_iter().collect())
                                .await
                            {
                                Ok(data) => {
                                    debug!(
                                        "Deleted {} records from database {:?}",
                                        found_items, data
                                    )
                                }
                                Err(error) => {
                                    error!(
                                        ?error,
                                        "Failed to delete {} records from database", found_items
                                    )
                                }
                            };
                            debug!("Sent total {} events to server", found_items);
                        }
                    } else {
                        debug!("Sent All pending data to server");
                        break;
                    }
                }
                Err(error) => {
                    error!(?error, "Failed to fetch all fs events");
                    break;
                }
            };
        }
    }
}

#[async_trait]
impl AgentRunnable for FIMDataSyncAgent<'static> {
    fn get_name(&self) -> &str {
        "fim_data_sync"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting FIM Data Sync Agent ------------------------");
        let receiver_arc = self.stop_signal_receiver.clone();

        let mut receiver = receiver_arc.lock().await;
        loop {
            select! {
                biased;

                _ = receiver.recv() => {
                    info!("Shutting Down FIM Data sync Agent");
                    break;
                }

                _ = sleep(Duration::from_secs(self.agent_metadata
                    .get_agent_refresh_settings()
                    .file_events_refresh_cycle)) => {
                    debug!("Checking for pending file events");
                    FIMDataSyncAgent::send_all_pending_events(self.agent_metadata).await;

                },
            }
        }
        info!("---------------------- Stopped FIM Data sync Agent ------------------------");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if let Err(error) = self.stop_signal_sender.send(true).await {
            error!(?error, "Failed to send stop signal to fim data sync agent");
        };
        Ok(())
    }

    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
}
