use agent_manager::Agent<PERSON>unnable;
use anyhow::Result;
use api::data_collection::send_fim_data;
use async_trait::async_trait;
use database::{models::AgentMetadata, Model};
use fim::FIMEvent;
use logger::{debug, error};
use logger::{info, trace};
use serde_json::json;
use std::time::Duration;
use tokio::select;
use tokio::time::sleep;
use utils::shutdown::{get_shutdown_signal, is_system_running};

pub struct FIMDataSyncAgent<'a> {
    agent_metadata: &'a AgentMetadata,
}

impl<'a> FIMDataSyncAgent<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> FIMDataSyncAgent<'a> {
        FIMDataSyncAgent { agent_metadata }
    }

    async fn send_all_pending_events(agent_metadata: &AgentMetadata) {
        loop {
            if !is_system_running() {
                break;
            }
            match FIMEvent::default().get_all(Some(100)).await {
                Ok(items) => {
                    let found_items = items.len();
                    if found_items > 0 {
                        if let Err(error) = send_fim_data(json!({
                                "asset_id" : agent_metadata.get_endpoint_id(),
                                "data" : json!({
                                    "fim_events": items,
                                })
                        }))
                        .await
                        {
                            error!(
                                ?error,
                                "Failed to send {} events to server and will be added back to",
                                found_items
                            );
                        } else {
                            match FIMEvent::default()
                                .delete_selected(items.into_iter().collect())
                                .await
                            {
                                Ok(data) => {
                                    debug!(
                                        "Deleted {} records from database {:?}",
                                        found_items, data
                                    )
                                }
                                Err(error) => {
                                    error!(
                                        ?error,
                                        "Failed to delete {} records from database", found_items
                                    )
                                }
                            };
                            debug!("Sent total {} events to server", found_items);
                        }
                    } else {
                        trace!("Sent All pending data to server");
                        break;
                    }
                }
                Err(error) => {
                    error!(?error, "Failed to fetch all fs events");
                    break;
                }
            };
        }
    }
}

#[async_trait]
impl AgentRunnable for FIMDataSyncAgent<'static> {
    fn get_name(&self) -> &str {
        "fim_data_sync"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting FIM Data Sync Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();

        FIMDataSyncAgent::send_all_pending_events(self.agent_metadata).await;

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down FIM Data sync Agent");
                    break;
                }

                _ = sleep(Duration::from_secs(self.agent_metadata
                    .get_agent_refresh_settings()
                    .file_events_refresh_cycle)) => {
                    trace!("Checking for pending file events");
                    FIMDataSyncAgent::send_all_pending_events(self.agent_metadata).await;

                },
            }
        }
        info!("---------------------- Stopped FIM Data sync Agent ------------------------");
        Ok(())
    }
}
