use crate::extension_agent::ExtensionAgent;
use agent_manager::{<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Agent<PERSON><PERSON><PERSON><PERSON>};
use anyhow::Result;
use async_trait::async_trait;
use data_collection::Certificates;
use data_collection::NetworkInterfaces;
use data_collection::OsServices;
use data_collection::Processes;
use data_collection::Resources;
use data_collection::Softwares;
use data_collection::StartUpItems;
use data_collection::Users;
use database::models::AgentMetadata;
use logger::error;
use logger::info;
use std::sync::Arc;
use tokio::select;
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::sync::Mutex;

pub struct DataCollectionAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    stop_signal_sender: Sender<bool>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl<'a> DataCollectionAgent<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> DataCollectionAgent<'a> {
        let (stop_signal_sender, stop_signal_receiver) = channel(1);
        DataCollectionAgent {
            agent_metadata,
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            stop_signal_sender,
        }
    }
}

#[async_trait]
impl AgentRunnable for DataCollectionAgent<'static> {
    fn get_name(&self) -> &str {
        "data_collection_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting Data Collection Agent ------------------------");
        let receiver_arc = self.stop_signal_receiver.clone();

        let mut receiver = receiver_arc.lock().await;

        let agent_metadata = self.agent_metadata;

        let mut agent_manager = AgentManager::new();

        // system resources agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            Resources::new(agent_metadata),
        )))));

        // certificate agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            Certificates::new(agent_metadata),
        )))));

        // network interface agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            NetworkInterfaces::new(agent_metadata),
        )))));

        // startup items agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            StartUpItems::new(agent_metadata),
        )))));

        // users agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            Users::new(agent_metadata),
        )))));

        // software agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            Softwares::new(agent_metadata),
        )))));

        // services agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            OsServices::new(agent_metadata),
        )))));

        // process agent
        agent_manager.add_agent(Agent::new(Box::new(ExtensionAgent::new(Box::new(
            Processes::new(agent_metadata),
        )))));

        loop {
            select! {
                biased;

                _ = receiver.recv() => {
                    agent_manager.stop().await;
                    info!("Shutting down Data Collection Agent");
                    break;
                }

                _ = agent_manager.start() => {
                    info!("Finished running all extensions");
                }

            }
        }
        info!("---------------------- Stopped Data Collection Agent ------------------------");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if let Err(error) = self.stop_signal_sender.send(true).await {
            error!(
                ?error,
                "Failed to send stop signal to data collection agent"
            );
        }
        Ok(())
    }

    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
}
