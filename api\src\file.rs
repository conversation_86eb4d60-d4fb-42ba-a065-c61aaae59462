use crate::{get_client, ApiClient, ApiError};
use anyhow::Result;
use async_speed_limit::Limiter;
use database::models::FileAttachment;
use logger::{debug, error, info};
use regex::Regex;
use reqwest::{header, Url};
use serde::Deserialize;
use shell::ShellCommand;
use std::path::{Path, PathBuf};
use tokio::{
    fs::{self, File},
    io::{self, AsyncWriteExt, BufReader},
};
use tokio_stream::StreamExt;
use tokio_util::io::StreamReader;
use urlencoding::decode;

#[derive(Debug, Deserialize)]
pub struct UploadedFile {
    #[serde(alias = "file-ref")]
    pub file_ref: String,
    #[serde(alias = "file-name")]
    pub file_name: String,
}

async fn download_at_throttled_speed<P: AsRef<Path>>(
    stream: impl futures::Stream<Item = Result<bytes::Bytes, ApiError>>,
    destination: P,
    limiter: Limiter,
) -> Result<(), ApiError> {
    tokio::pin!(stream);

    let reader = StreamReader::new(
        stream.map(|res| res.map_err(|err| std::io::Error::new(std::io::ErrorKind::Other, err))),
    );

    let mut limited_reader = BufReader::new(limiter.limit(reader));

    let mut destination_file = File::create(destination.as_ref()).await?;

    io::copy(&mut limited_reader, &mut destination_file).await?;

    destination_file.flush().await?;
    info!(
        "File has been saved at location {}",
        destination.as_ref().display()
    );
    Ok(())
}

async fn zirozen_file(
    attachment: &FileAttachment,
    destination: &Path,
    limiter: Limiter,
) -> Result<FileAttachment, ApiError> {
    let default_download_url = format!("/patch/download/{}", attachment.ref_name);
    let stream = get_client()?
        .stream(
            attachment
                .zirozen_download_url
                .as_ref()
                .unwrap_or(&default_download_url),
            attachment.request_method.as_ref(),
            attachment.request_body.as_ref(),
            60,
        )
        .await?;

    download_at_throttled_speed(stream, destination, limiter).await?;
    // @TODO match checksum here
    Ok(attachment.to_owned())
}

async fn public_file(
    mut attachment: FileAttachment,
    limiter: Limiter,
) -> Result<FileAttachment, ApiError> {
    let url = attachment.public_url.as_ref().unwrap();

    info!("Downloading file from url {}", url);

    let response = reqwest::get(url).await?;

    let url_path = Url::parse(url).map_err(|_| ApiError::UrlParseError(url.clone()))?;

    let file_path = PathBuf::from(url_path.path());

    let file_name = if file_path.extension().is_some() {
        if let Some(file_path) = file_path.file_name() {
            attachment.real_name = decode(file_path.to_str().unwrap())
                .map_err(|_| ApiError::UrlDecodeError(file_path.to_str().unwrap().to_owned()))?
                .to_string()
                .replace(" ", "-");
            Some(&attachment.real_name)
        } else {
            None
        }
    } else {
        let content_disposition = response
            .headers()
            .get(header::CONTENT_DISPOSITION)
            .and_then(|disposition| {
                disposition
                    .to_str()
                    .ok()
                    .filter(|content| content.contains("filename="))
            });

        if let Some(content_disposition) = content_disposition {
            let re = Regex::new(r"filename=(.+)").unwrap();
            let mut filename: String = "".to_owned();
            for cap in re.captures_iter(content_disposition) {
                filename = cap[1].replace('"', "");
            }

            if !filename.is_empty() {
                attachment.real_name = filename.replace(" ", "-");
                Some(&attachment.real_name)
            } else {
                None
            }
        } else {
            None
        }
    };

    if let Some(file_name) = file_name {
        let file_path = attachment.local_path.as_ref().unwrap().join(file_name);

        download_at_throttled_speed(
            ApiClient::wrap_stream_to_timeout(response.bytes_stream(), 60),
            file_path.as_path(),
            limiter,
        )
        .await?;

        return Ok(attachment);
    }

    Err(ApiError::UnableToGetFileName(format!(
        "Failed to fetch package from url {}. \n response headers are {:?}",
        attachment.ref_name,
        response.headers()
    )))
}

async fn download_shared_drive(
    mut attachment: FileAttachment,
    destination: &Path,
) -> Result<FileAttachment, ApiError> {
    info!("Downloading file from url {}", attachment.ref_name);

    let network_path = PathBuf::from(&attachment.ref_name);

    let file_name = network_path.file_name();

    if file_name.is_none() {
        Err(ApiError::UnableToGetFileName(format!(
            "Failed to find file name from path unable to download file form path {}",
            attachment.ref_name
        )))
    } else {
        attachment.real_name = file_name
            .unwrap()
            .to_str()
            .unwrap()
            .to_owned()
            .replace(" ", "-");

        let command = {
            cfg_if! {
                if #[cfg(windows)] {
                    r#"powershell.exe Copy-Item -Path "_SOURCE_" -Destination "_DESTINATION_""#
                        .replace("_SOURCE_", &attachment.ref_name)
                        .replace("_DESTINATION_", &attachment.real_name)
                } else {
                    if let Some(source_path) = network_path.parent() {
                        cfg_if! {
                            if #[cfg(target_os = "macos")] {
                                use tokio::fs;

                                fs::create_dir_all(destination.join("shared_folder")).await?;
                                format!(
                                    r#"mount_smbfs -N "{}" "{}" && cp "{}" "{}" && umount "{}""#,
                                    source_path.display(),
                                    destination.join("shared_folder").display(),
                                    destination
                                        .join("shared_folder")
                                        .join(&attachment.real_name)
                                        .display(),
                                    destination.join(&attachment.real_name).display(),
                                    destination.join("shared_folder").display()
                                )
                            } else {
                                format!(
                                    r#"smbclient '{}' -N -c 'get "{}"'"#,
                                    source_path.display(),
                                    &attachment.real_name
                                )
                            }
                        }
                    } else {
                        return Err(ApiError::UnableToMountSharedDrive(format!(
                            "Unable to mount shared drive {}",
                            attachment.ref_name
                        )));
                    }
                }
            }
        };

        let mut shell_command = ShellCommand::new(&command);
        let output = shell_command
            .cwd(destination)
            .with_output_file(destination.join("output.txt").as_path())
            .run()?;

        if output.succeeded() {
            info!("File has been saved at location {}", &attachment.real_name);
            Ok(attachment)
        } else {
            Err(ApiError::UnableToSaveFileFromNetwork(format!(
                "Failed to download software from url {}",
                attachment.ref_name
            )))
        }
    }
}

pub async fn download(
    mut attachment: FileAttachment,
    limiter: Limiter,
) -> Result<FileAttachment, ApiError> {
    if attachment.local_path.is_none() {
        return Err(ApiError::AttachmentLocationNotSet(attachment.clone()));
    }

    if !attachment.real_name.is_empty() {
        // cleanup attachment name
        attachment.real_name = attachment.real_name.replace(" ", "-");
    }

    let destination_dir = attachment.local_path.as_ref().unwrap();

    if destination_dir.exists() == false {
        // create destination directory
        debug!(
            "Creating directory to save attachment {}",
            destination_dir.display()
        );
        fs::create_dir_all(&destination_dir).await?;
    }

    let destination = destination_dir.join(&attachment.real_name);

    // if !attachment.real_name.is_empty() && Path::new(&destination).exists() {
    //     debug!(
    //         "File is already available at path {}",
    //         &destination.display()
    //     );
    //     return Ok(attachment);
    // }

    if attachment.is_network_share.is_some_and(|value| value) {
        debug!("Downloading shared drive file {:?}", attachment);
        download_shared_drive(attachment, destination.as_path()).await
    } else if attachment.ref_name.len() > 0 {
        debug!("Downloading zirozen file {}", attachment.ref_name);
        zirozen_file(&attachment, destination.as_path(), limiter).await
    } else if attachment.public_url.is_some() {
        debug!(
            "Downloading public url file {}",
            attachment.public_url.as_ref().unwrap()
        );
        public_file(attachment, limiter).await
    } else {
        error!(
            "Api Error: Unable to determined from where to download attachment {:?}",
            attachment
        );
        Err(ApiError::InvalidAttachment(attachment.clone()))
    }
}

pub async fn upload(path: &Path) -> Result<UploadedFile, ApiError> {
    get_client()?
        .upload::<UploadedFile, _>("/upload", path)
        .await
}
