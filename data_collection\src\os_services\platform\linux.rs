use crate::{os_services::os_service::OsService, platform::systemd_units};
use std::collections::HashSet;

pub fn get_os_services() -> HashSet<OsService> {
    systemd_units()
        .into_iter()
        .map(|item| OsService {
            name: item.name,
            description: item.description,
            status: item.status,
            cmdline: item.path,
            start_type: item.load_state,
        })
        .collect()
}
