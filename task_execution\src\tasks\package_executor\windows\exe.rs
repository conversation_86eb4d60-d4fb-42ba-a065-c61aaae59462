use crate::{has_commands::HasCommands, tasks::command_executor::CommandExecutor, TaskExecutable};
use anyhow::Error;
use database::models::FileAttachment;
use shell::ShellOutput;

pub struct Exe<'a> {
    commands: Box<dyn HasCommands>,
    attachment: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Exe<'a> {
    pub fn new(
        commands: Box<dyn HasCommands>,
        attachment: &'a FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            commands,
            attachment,
            task,
        }
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        let command = self
            .commands
            .get_install_command()
            .map(|i| i.to_owned())
            .unwrap_or(format!("\"{}\" /S", self.attachment.real_name));

        Ok(CommandExecutor::new_command(&command, self.task)
            .execute()
            .await?)
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        let command = self
            .commands
            .get_uninstall_command()
            .map(|i| i.to_owned())
            .unwrap_or(format!("\"{}\"", self.attachment.real_name));

        Ok(CommandExecutor::new_command(&command, self.task)
            .execute()
            .await?)
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        let command = self
            .commands
            .get_upgrade_command()
            .map(|i| i.to_owned())
            .unwrap_or(format!("\"{}\" /S", self.attachment.real_name));

        Ok(CommandExecutor::new_command(&command, self.task)
            .execute()
            .await?)
    }
}
