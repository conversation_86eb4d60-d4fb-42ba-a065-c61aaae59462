[package]
name = "windows_patch_xml_checker"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
shell = { path = "../shell" }
database = { path = "../database" }
windows_registry = { path = "../windows_registry" }
utils = { path = "../utils" }
thiserror = "2.0.4"
anyhow = { version = "1.0.94", features = ["backtrace"] }
serde = { version = "1.0.219", features = ["derive"] }
tokio = { version = "1.43.0", features = ["full", "tracing"] }
futures = { version = "0.3.31", features = ["executor"] }
tokio-util = "0.7.13"
quick-xml = { version = "0.38.0", features = ["serialize"] }
evalexpr = "12.0.1"
wmi = "0.15"
version-compare = "0.2.0"
windows = { version = "0.59.0", features = [
  "Win32_UI_Shell",
  "Win32_Foundation",
  "Win32_Storage_FileSystem",
] }
chrono = "0.4.39"
serde_json = "1.0.137"
dashmap = { version = "6.1.0", features = ["rayon"] }
regex = "1.11.1"
