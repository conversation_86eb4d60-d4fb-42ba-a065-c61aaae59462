use crate::{
    ApplicabilityRulesEvaluationConfig, Cbs<PERSON>nstalled<PERSON><PERSON><PERSON>, ParsedPackage, PatchStatus,
    RelationEvaluationConfig, UpdateType, WindowsXmlCheckerError,
};
use dashmap::DashMap;
use logger::{debug, error};
use serde::Deserialize;
use serde_xml_rs::{Deserializer, EventReader, ParserConfig};
use std::{fs::File, path::PathBuf, sync::Arc};
use utils::dir::get_patch_dir;
use win_registry::WinRegistry;

#[derive(Debug, Default)]
pub struct WindowsUpdate {
    status: PatchStatus,
    installed: bool,
    installable: bool,
    parsed_package: Option<ParsedPackage>,
    category: String,
    sub_kbs: Vec<String>,
    uuid: String,
    level: usize,
    directory: PathBuf,
    prerequesite: bool,
    scanned_output: Arc<String>,
    update_history: Arc<String>,
    wmic_qfe_output: Arc<String>,
    result_cache: Arc<DashMap<String, u8>>,
}

impl WindowsUpdate {
    pub fn new() -> Self {
        Self {
            directory: get_patch_dir().as_ref().to_owned(),
            ..Default::default()
        }
    }

    pub fn uuid(mut self, uuid: String) -> Self {
        self.uuid = uuid;

        self
    }

    pub fn with_result_cache(mut self, result_cache: Arc<DashMap<String, u8>>) -> Self {
        self.result_cache = result_cache;

        self
    }

    pub fn is_third_party_patch(&self) -> bool {
        self.get_parsed_package().is_third_party_patch()
    }

    pub fn scanned_output(mut self, scan_output: Arc<String>) -> Self {
        self.scanned_output = scan_output;

        self
    }

    pub fn update_history(mut self, update_history: Arc<String>) -> Self {
        self.update_history = update_history;

        self
    }

    pub fn wmic_qfe_output(mut self, wmic_qfe_output: Arc<String>) -> Self {
        self.wmic_qfe_output = wmic_qfe_output;

        self
    }

    pub fn category(mut self, category: String) -> Self {
        self.category = category;

        self
    }

    pub fn sub_kb(mut self, sub_kb_ids: Vec<String>) -> Self {
        self.sub_kbs = sub_kb_ids;

        self
    }

    pub fn level(mut self, level: usize) -> Self {
        self.level = level;

        self
    }

    pub fn prerequisite(mut self, is_prerequesite: bool) -> Self {
        self.prerequesite = is_prerequesite;

        self
    }

    pub fn path(mut self, path: PathBuf) -> Self {
        self.directory = path;

        self
    }

    fn get_parsed_package(&self) -> &ParsedPackage {
        if self.parsed_package.is_none() {
            error!("get_parsed_package is called before parse");
        }
        self.parsed_package.as_ref().unwrap()
    }

    pub fn parse(mut self) -> Result<Self, WindowsXmlCheckerError> {
        let config = ParserConfig::new()
            .trim_whitespace(true)
            .whitespace_to_characters(false)
            .ignore_comments(true);

        let file_path = if self
            .directory
            .join(format!("{}.xml", self.uuid.to_uppercase()))
            .exists()
        {
            self.directory
                .join(format!("{}.xml", self.uuid.to_uppercase()))
        } else if self
            .directory
            .join(format!("{}.xml", self.uuid.to_lowercase()))
            .exists()
        {
            self.directory
                .join(format!("{}.xml", self.uuid.to_lowercase()))
        } else {
            self.directory.join(format!("{}.xml", self.uuid))
        };

        let file = File::open(file_path)?;

        let event_reader = EventReader::new_with_config(file, config);
        let update = ParsedPackage::deserialize(&mut Deserializer::new(event_reader))?;

        self.parsed_package = Some(update);

        debug!(
            "Parsed Package with uuid {} and sub_kbs {:?} from xml file \n {:?}",
            self.parsed_package.as_ref().unwrap().get_uuid(),
            self.sub_kbs,
            self.parsed_package.as_ref().unwrap()
        );

        Ok(self)
    }

    pub fn process_kb(kb_id: String) -> String {
        if kb_id.to_lowercase() == "none" {
            kb_id
        } else {
            format!("KB{}", kb_id.to_lowercase().replace("kb", "")).to_owned()
        }
    }

    fn check_kb_in_scanned_ouput(&self, kb_id: &str) -> bool {
        self.get_parsed_package().get_update_type() == UpdateType::Software
            && self.scanned_output.contains(kb_id)
    }

    fn check_kb_in_update_history(&self, kb_id: Option<&str>) -> bool {
        let processed_kb = WindowsUpdate::process_kb(
            kb_id.map_or(self.get_parsed_package().get_kb(), |f| f.to_owned()),
        );
        self.update_history.contains(&processed_kb) || self.wmic_qfe_output.contains(&processed_kb)
    }

    fn has_uuid_in_update_history(&self) -> bool {
        self.update_history
            .contains(self.get_parsed_package().get_uuid().to_lowercase().as_str())
    }

    fn is_kb_installed(&self, kb_id: Option<&str>) -> bool {
        let processed_kb = WindowsUpdate::process_kb(
            kb_id.map_or(self.get_parsed_package().get_kb(), |f| f.to_owned()),
        );
        debug!("Checking scanned output for kb {}", processed_kb);

        let mut is_installed = self.check_kb_in_scanned_ouput(&processed_kb);

        if is_installed == false && processed_kb.to_lowercase() != "none" {
            let cbs_checker = CbsInstalledChecker::new(&processed_kb, &self.wmic_qfe_output);
            is_installed = is_installed || cbs_checker.is_installed();
        }
        is_installed
    }

    fn prepare(&mut self) {
        debug!("============ Evaluating file {} ==============", self.uuid);
        self.installed = self.is_kb_installed(None);
        debug!(
            "Check installed status before processing of IS_INSTALLED {}",
            self.installed
        );
        debug!(
            "Before scanning xml uuid : {} isInstalled : {}",
            self.uuid, self.installed
        );
        self.installable = true;
    }

    pub fn evaluate(mut self) -> PatchStatus {
        if let Some(product_name) = self
            .get_parsed_package()
            .get_update_properites()
            .product_name
            .as_ref()
        {
            debug!(
                "Got product name from update properties {} checking windows registry key",
                product_name
            );
            let path = PathBuf::from(
                "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Update\\TargetingInfo\\Installed",
            ).join(product_name);
            if let Err(error) = WinRegistry::new(&path) {
                error!(
                    ?error,
                    "Failed to open registry key at path {}",
                    path.display()
                );
                self.status = PatchStatus::NotReady;
                return self.status;
            }
        }

        if self.has_uuid_in_update_history() {
            debug!(
                "Found UUID {} in update history so skipping everything and marking as installed",
                self.get_parsed_package().get_uuid().to_lowercase()
            );
            self.status = PatchStatus::Installed;
            return self.status;
        }
        self.prepare();
        self.evaluate_relationship_and_applicability_rules();
        self.check_sub_kb_ids();
        self.status
    }

    pub fn get_status(&self) -> PatchStatus {
        self.status.to_owned()
    }

    pub fn evaluate_relationship_and_applicability_rules(&mut self) {
        let mut is_prerequsite_satisfied = true;
        let mut is_bundled_update_satisfied = true;

        let parsed_package = self.get_parsed_package();
        let update_type = parsed_package.get_update_type();

        if parsed_package.has_relationship() {
            if parsed_package.has_prerequisites() {
                debug!("[L{}]============================================= Checking Prerequsite Relationship for uuid {} =============================================", self.level, self.uuid);
                let prerequsite_status = self
                    .get_parsed_package()
                    .evaluate_prerequsite_relationship(&RelationEvaluationConfig::new(
                        self.level,
                        self.scanned_output.clone(),
                        self.update_history.clone(),
                        self.result_cache.clone(),
                    ));
                debug!("Prerequsite is evaluated to {:?}", prerequsite_status);
                if prerequsite_status == PatchStatus::NotReady {
                    is_prerequsite_satisfied = false;
                }
            }

            if parsed_package.has_bundled_updates() {
                debug!("[L{}]============================================= Checking Bundled Updates Relationship for uuid {} =============================================", self.level, self.uuid);
                let bundled_update_status = self
                    .get_parsed_package()
                    .evaluate_bundled_relationship(&RelationEvaluationConfig::new(
                        self.level,
                        self.scanned_output.clone(),
                        self.update_history.clone(),
                        self.result_cache.clone(),
                    ));
                debug!(
                    "Bundled Updates is evaluated to {:?}",
                    bundled_update_status
                );
                if bundled_update_status == PatchStatus::NotReady {
                    is_bundled_update_satisfied = false;
                }
            }
        }

        if is_prerequsite_satisfied && is_bundled_update_satisfied {
            if parsed_package.has_applicability_rules() {
                // if (parsed_package.has_installed_applicability_rules() == false) {
                //     self.installed = true
                // }
                // Process Applicability
                let evaluation_result = parsed_package.evaluate_applicability_rules(
                    &ApplicabilityRulesEvaluationConfig::new(self.update_history.clone()),
                );
                debug!(
                    "Applicability Rule evaluation Result for uuid {} is {:?}",
                    self.uuid, evaluation_result
                );
                if parsed_package.get_update_type() == UpdateType::Detectoid {
                    debug!("Update type is detectoid so considering only direct rule result");
                    if evaluation_result.is_installed.is_some_and(|i| i) {
                        self.status = PatchStatus::Installed;
                    } else if evaluation_result.is_installable.is_some_and(|i| i) {
                        self.status = PatchStatus::Ready;
                    } else {
                        self.status = PatchStatus::NotReady;
                    }
                } else {
                    if let Some(is_installed) = evaluation_result.is_installed {
                        self.installed = self.installed || is_installed;
                    }
                    if let Some(is_installable) = evaluation_result.is_installable {
                        self.installable = is_installable;
                    } else {
                        // if no installable rules are given we will use default true
                        self.installable = true;
                    }
                    debug!(
                        "Self installed is {} and Self installable is {}",
                        self.installed, self.installable
                    );
                    if self.installable {
                        if self.installed {
                            self.status = PatchStatus::Ready;
                        } else {
                            self.status = PatchStatus::Installed;
                        }
                    } else {
                        if self.is_third_party_patch() {
                            if self.installed {
                                self.status = PatchStatus::Installed;
                            } else {
                                self.status = PatchStatus::NotReady;
                            }
                        } else {
                            self.status = PatchStatus::NotReady;
                        }
                    }
                    // if self.installed && self.installable {
                    //     self.status = PatchStatus::Ready;
                    // } else if self.installed {
                    //     self.status = PatchStatus::Installed;
                    // } else {
                    //     self.status = PatchStatus::NotReady;
                    // }
                    debug!(
                        "Status based on applicability rule for uuid {} is {}",
                        self.uuid, self.status
                    );
                }
            } else {
                if update_type == UpdateType::Software {
                    if !self.installed {
                        self.installable = true;
                    }
                    self.installed = self.installed || false;
                    self.status = PatchStatus::Ready;
                } else {
                    self.status = PatchStatus::Installed;
                }
                debug!(
                    "Status without applicability rules for uuid {} is {}",
                    self.uuid, self.status
                );
            }
        } else {
            self.status = PatchStatus::NotReady;
        }
        debug!("[L{}]============================================= uuid: {} and final status is {} =============================================", self.level, self.uuid, self.status);
    }

    fn check_sub_kb_ids(&mut self) {
        // check if actually installed
        self.status = self.get_evaluation_status();
        if self.status == PatchStatus::Ready {
            if self.check_kb_in_update_history(None) {
                self.status = PatchStatus::Installed;
                debug!(
                    "Update history has entry for this kb {}",
                    self.get_parsed_package().get_kb()
                );
            } else if self.sub_kbs.len() > 0 {
                let installed_sub_kbs = self
                    .sub_kbs
                    .iter()
                    .filter(|item| self.is_kb_installed(Some(item)))
                    .collect::<Vec<&String>>();
                if installed_sub_kbs.len() == 0 {
                    self.status = PatchStatus::Ready;
                } else if installed_sub_kbs.len() == self.sub_kbs.len() {
                    self.status = PatchStatus::Installed;
                } else {
                    self.status = PatchStatus::PartiallyInstalled;
                }
                debug!("Updated status from sub kbs {:?}", self.status);
            }
        }
    }

    fn get_evaluation_status(&self) -> PatchStatus {
        debug!(
            "Before calling get_evaluation_status the status is {}",
            self.status
        );
        if self.status == PatchStatus::Ready {
            if self.installed {
                if self.installable {
                    debug!("is_installed true and is_installable true so setting status ready");
                    return PatchStatus::Ready;
                }
                debug!("is_installed true so setting status installed");
                return PatchStatus::Installed;
            } else {
                if self.installable {
                    debug!("is_installable true so setting status ready");
                    return PatchStatus::Ready;
                } else {
                    debug!(
                        "is_installed false and is_installable false so setting status NotReady"
                    );
                    return PatchStatus::NotReady;
                }
            }
        } else {
            self.status.to_owned()
        }
    }
}
