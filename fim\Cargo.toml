[package]
name = "fim"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
database = { path = "../database" }
api = { path = "../api" }
utils = { path = "../utils" }
thiserror = "2.0.4"
tokio = { version = "1.43.0", features = ["full", "tracing"] }
anyhow = { version = "1.0.94", features = ["backtrace"] }
tokio-stream = "0.1.17"
serde = "1.0.219"
chrono = "0.4.39"
serde_json = "1.0.139"
rayon = "1.10.0"
glob = "0.3.2"
globset = "0.4.15"
ignore = "0.4.23"
async-trait = "0.1.87"
futures = "0.3.31"
notify-debouncer-full = "0.5.0"

[target.'cfg(windows)'.dependencies]
windows_driver_handler = { path = "../windows_driver_handler" }
