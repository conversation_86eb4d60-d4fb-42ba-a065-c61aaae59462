[package]
name = "api"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
database = { path = "../database" }
agent_manager = { path = "../agent_manager" }
shell = { path = "../shell" }
reqwest = { version = "0.12.9", features = [
  "json",
  "stream",
  "rustls-tls",
  "multipart",
], default-features = false }
serde_json = "1.0.133"
serde = { version = "1.0.219", features = ["derive"] }
reqwest-middleware = { version = "0.4.0", features = ["json", "multipart"] }
reqwest-retry = "0.7.0"
thiserror = "2.0.4"
anyhow = { version = "1.0.94", features = ["backtrace"] }
tokio = { version = "1.43.0", features = ["full", "tracing"] }
bytes = "1.9.0"
futures = "0.3.31"
tokio-stream = "0.1.17"
urlencoding = "2.1.3"
regex = "1.11.1"
http = "1.2.0"
async-trait = "0.1.87"
cfg-if = "1.0.0"
async-speed-limit = { version = "0.4.2", features = ["tokio"] }
tokio-util = "0.7.15"
