use crate::{
    has_task::HasTask, log_task::LogTask, sync_task::SyncTask,
    tasks::attachment_downloader::AttachmentDownloader, TaskExecutable, TaskExecutionError, Unzip,
};
use anyhow::Result;
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{FileAttachment, Task, TaskStatus},
};
use logger::{debug, error, info, ModuleLogger};
use serde_json::json;
use std::sync::Arc;
use tokio::{fs, process::Command};
use utils::{
    constants::{ENDPOINTOPS_BINARY_NAME, MESH_AGENT_BINARY_NAME},
    dir::get_current_dir,
    installation::copy_installation_files,
};

fn get_platform() -> String {
    if cfg!(windows) {
        "windows"
    } else if cfg!(target_os = "linux") {
        "linux"
    } else if cfg!(target_os = "macos") {
        "mac"
    } else {
        ""
    }
    .to_owned()
}

fn get_arch() -> String {
    if cfg!(target_arch = "arm") || cfg!(target_arch = "aarch64") {
        debug!("Running on ARM architecture");
        "arm64"
    } else {
        debug!("Running on x86_64 architecture");
        "x64"
    }
    .to_owned()
}

#[derive(Debug)]
pub struct UpgradeTask {
    task: Task,
    logger: Arc<ModuleLogger>,
}

impl UpgradeTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            logger: Arc::new(ModuleLogger::new(
                "upgrade",
                None,
                Some("upgrade".to_owned()),
            )),
        }
    }
}

impl HasTask for UpgradeTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for UpgradeTask {
    fn get_logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for UpgradeTask {}

#[async_trait]
impl TaskExecutable for UpgradeTask {
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            format!("Initiating execution of upgrade {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        let platform = get_platform();

        debug!("Got platform {}", platform);

        if platform.is_empty() {
            error!("Could not determine current platform");
            return Err(TaskExecutionError::UnsupportedPlatform.into());
        }

        let arch = get_arch();

        if arch.is_empty() {
            error!("Could not determine current cpu arch");
            return Err(TaskExecutionError::UnsupportedPlatform.into());
        }

        let upgrade_dir = get_current_dir().join("upgrade");

        if upgrade_dir.exists() == false {
            fs::create_dir_all(&upgrade_dir).await?;
        }

        let upgrade_attachment = FileAttachment {
            ref_name: "upgrade.zip".to_owned(),
            real_name: "upgrade.zip".to_owned(),
            request_method: Some("POST".to_owned()),
            request_body: Some(json!({ "platform": platform, "arch": arch })),
            local_path: Some(Box::from(
                get_current_dir().join("upgrade").as_path().to_owned(),
            )),
            zirozen_download_url: Some(format!("/inventory/enroll/agent/{}", 1)),
            ..Default::default()
        };

        let attachment =
            match AttachmentDownloader::new(upgrade_attachment.to_owned(), Box::new(self), None)
                .download()
                .await
            {
                Ok(attachment) => {
                    debug!("File has been downloaded {:?}", attachment);
                    attachment
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to download upgrade file {:?}", upgrade_attachment
                    );
                    task_result.exit_code = 99;
                    task_result.status = TaskStatus::Failed;
                    task_result.output = format!(
                        "Failed to download patch file {:?} with error {:?}",
                        upgrade_attachment, error
                    );
                    return Ok(task_result);
                }
            };

        // unzip attachment
        Unzip::new(&attachment, Box::new(self))
            .extract(Some(upgrade_dir.clone()))
            .await?;

        let upgrade_source_dir = upgrade_dir.join(format!("{}_{}", get_platform(), get_arch()));

        let current_installation_dir = get_current_dir();

        debug!("Source Dir {}", upgrade_source_dir.display());

        // if mesh agent is available in upgrade then uninstall old one
        if upgrade_source_dir.join(MESH_AGENT_BINARY_NAME).exists() {
            info!("Found rdp agent in upgrade so uninstalling old rdp agent");
            // here exe self replace, uninstall previous rdp agent and copy file logic goes
            let rdp_uninstall = Command::new(
                current_installation_dir
                    .join(MESH_AGENT_BINARY_NAME)
                    .to_string_lossy()
                    .to_string(),
            )
            .arg("-uninstall")
            .output()
            .await;

            match rdp_uninstall {
                Ok(result) => {
                    info!(
                        "Output of rdp uninstallation {}",
                        String::from_utf8_lossy(&result.stdout)
                    );
                    self.write_task_log(
                        format!(
                            "Output of rdp uninstallation {}",
                            String::from_utf8_lossy(&result.stdout)
                        ),
                        None,
                    )
                    .await;
                }
                Err(error) => {
                    error!(?error, "Failed to execute -uninstall on rdp agent");
                    self.write_task_log(
                        format!("Failed to execute -uninstall on rdp agent {:?}", error),
                        Some("ERROR"),
                    )
                    .await;
                    task_result.exit_code = 5;
                    task_result.status = TaskStatus::Failed;
                    return Ok(task_result);
                }
            }
        }

        #[cfg(windows)]
        {
            use utils::constants::DRIVER_FILE_NAME;
            use utils::windows_driver_utility;

            if upgrade_source_dir.join(DRIVER_FILE_NAME).exists() {
                info!("Found Driver file in upgrade so uninstalling old driver if available");
                if windows_driver_utility::is_installed() {
                    info!("Driver is installed so uninstalling it");
                    if let Err(error) = windows_driver_utility::uninstall() {
                        error!(?error, "Failed to uninstall current driver");
                        self.write_task_log(
                            format!("Failed to uninstall current driver {:?}.", error),
                            Some("ERROR"),
                        )
                        .await;
                    } else {
                        self.write_task_log(format!("Driver uninstalled successfully."), None)
                            .await;
                    }
                } else {
                    info!("Driver is not installed so skipping uninstallation");
                    self.write_task_log(
                        format!("Driver is not installed so skipping uninstallation"),
                        None,
                    )
                    .await;
                }
            }
        }

        copy_installation_files(
            upgrade_source_dir.as_ref(),
            &current_installation_dir.as_ref(),
            vec![ENDPOINTOPS_BINARY_NAME],
        )
        .await?;

        self.write_task_log(
            format!("Upgrade Agent Files have been copied successfully"),
            None,
        )
        .await;

        // if windows then we need to reinstall device driver as well
        #[cfg(windows)]
        {
            use utils::{constants::DRIVER_FILE_NAME, windows_driver_utility};

            if upgrade_source_dir.join(DRIVER_FILE_NAME).exists() {
                info!("Installing updated driver");
                if let Err(error) = windows_driver_utility::install(
                    get_current_dir()
                        .as_ref()
                        .to_string_lossy()
                        .to_string()
                        .as_str(),
                ) {
                    error!(?error, "Failed to install updated driver");
                    self.write_task_log(
                        format!("Failed to install updated driver {:?}.", error),
                        Some("ERROR"),
                    )
                    .await;
                } else {
                    info!("Driver Update installed successfully.");
                    self.write_task_log(format!("Driver Update installed successfully."), None)
                        .await;
                }
            }
        }

        debug!("Replacing endpointops binary");
        // replace endpointops binary itself
        match self_replace::self_replace(&upgrade_source_dir.join(ENDPOINTOPS_BINARY_NAME)) {
            Err(error) => {
                error!(
                    ?error,
                    "Failed to delete binary out side of install directory"
                );
                self.write_task_log(
                    format!(
                        "Failed to delete binary out side of install directory {:?}",
                        error
                    ),
                    Some("ERROR"),
                )
                .await;
                task_result.exit_code = 4;
                task_result.status = TaskStatus::Failed;
                return Ok(task_result);
            }
            _ => {}
        };

        task_result.exit_code = 0;
        task_result.status = TaskStatus::Success;

        Ok(task_result)
    }
}
