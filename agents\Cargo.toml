[package]
name = "agents"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
data_collection = { path = "../data_collection" }
agent_manager = { path = "../agent_manager" }
task_execution = { path = "../task_execution" }
utils = { path = "../utils" }
database = { path = "../database" }
api = { path = "../api" }
shell = { path = "../shell" }
fim = { path = "../fim" }
auth_event = { path = "../auth_event" }
anyhow = { version = "1.0.94", features = ["backtrace"] }
async-trait = "0.1.83"
tokio = { version = "1.43.0", features = ["full", "tracing"] }
serde_json = "1.0.134"
sysinfo = "0.33.1"
serde = "1.0.219"
thiserror = "2.0.4"
futures = "0.3.31"
tokio-stream = "0.1.17"
chrono = "0.4.39"
