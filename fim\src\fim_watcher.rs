use crate::{fim_event::<PERSON><PERSON><PERSON><PERSON>, <PERSON>IM<PERSON><PERSON><PERSON>, SystemTimedEvent, Watcher};
use async_trait::async_trait;
use database::{models::FIMConfig, Model};
use globset::{Glob, GlobSet, GlobSetBuilder};
use ignore::WalkBuilder;
use logger::{debug, error, info, trace, ModuleLogger, WithSubscriber};
use notify_debouncer_full::{
    new_debouncer, notify::RecursiveMode, DebounceEventResult, DebouncedEvent,
};
use std::{
    collections::HashSet,
    path::{Path, PathBuf},
    sync::Arc,
    time::Duration,
};
use tokio::{
    sync::{
        broadcast::Receiver,
        mpsc::{unbounded_channel, UnboundedReceiver},
        Mutex,
    },
    task::{JoinError, JoinHandle},
    time::Instant,
};
use tokio_stream::{wrappers::UnboundedReceiverStream, StreamExt};
use utils::{dir::get_log_dir, shutdown::is_system_running};

pub struct FIMWatcher {
    logger: <PERSON><PERSON><PERSON>Log<PERSON>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
    config: Arc<FIMConfig>,
}

impl FIMWatcher {
    pub fn glob_root(pattern: &str) -> PathBuf {
        let path = Path::new(pattern);

        // Find the first component that contains a glob character
        let mut root = PathBuf::new();
        for component in path.components() {
            let comp_str = component.as_os_str().to_string_lossy();
            if comp_str.contains('*')
                || comp_str.contains('?')
                || comp_str.contains('[')
                || comp_str.contains('{')
            {
                break; // Stop at the first wildcard
            }
            root.push(component);
        }

        // Default to current directory if no root found
        if root.as_os_str().is_empty() {
            PathBuf::from(".")
        } else {
            root
        }
    }

    pub fn new(config: FIMConfig, stop_signal_receiver: Receiver<bool>) -> Self {
        Self {
            logger: ModuleLogger::new(
                "fim",
                None,
                Some(
                    get_log_dir()
                        .join(format!("fim/{}-{}", config.id(), config.category()))
                        .to_string_lossy()
                        .to_string(),
                ),
            ),
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            config: Arc::new(config),
        }
    }

    fn should_exclude_event(excluded_paths: GlobSet, path: Option<&PathBuf>) -> bool {
        if path.is_none() {
            return true;
        }
        let event_path_buf = path.unwrap();
        let event_path = event_path_buf.to_string_lossy().to_string();
        if event_path.is_empty() {
            return true;
        }
        excluded_paths.is_match(event_path_buf)
    }

    fn start_event_receiver(
        &self,
        tokio_rx: UnboundedReceiver<Vec<DebouncedEvent>>,
    ) -> JoinHandle<()> {
        let mut globset = GlobSetBuilder::new();
        for excluded_path in self.config.excluded_paths() {
            if let Ok(pat) = Glob::new(excluded_path.to_str().unwrap_or_default()) {
                globset.add(pat);
            }
        }
        let glob_matcher = match globset.build() {
            Ok(glob) => glob,
            Err(error) => {
                error!(?error, "Failed to build glob set");
                GlobSet::default()
            }
        };
        let fim_config = self.config.clone();
        let logger = self.logger.clone();
        tokio::task::Builder::new()
            .name("file_event_stream")
            .spawn(
                async move {
                    let mut stream = UnboundedReceiverStream::new(tokio_rx);

                    while let Some(events) = stream.next().await.take_if(|_| is_system_running()) {
                        let events = events
                            .into_iter()
                            .take_while(|_| is_system_running())
                            .filter(|item| {
                                if item.event.paths.len() == 0 {
                                    false
                                } else {
                                    FIMWatcher::should_exclude_event(
                                        glob_matcher.clone(),
                                        item.event.paths.first(),
                                    ) == false
                                }
                            })
                            .map(|event| SystemTimedEvent::new(event.event, &fim_config, &logger))
                            .map(|item| item.build_fim_event())
                            .collect::<HashSet<FIMEvent>>();
                        if is_system_running() {
                            FIMEvent::default().bulk_insert(events).await;
                        }
                    }

                    info!("Event Loop exited");
                }
                .with_subscriber(self.logger.subscriber()),
            )
            .unwrap()
    }

    async fn collect_paths(&self) -> Result<Vec<PathBuf>, JoinError> {
        let config_clone = self.config.clone();
        let logger = self.logger.clone();
        debug!("Calculating paths to watch from glob patterns");
        tokio::task::Builder::new()
            .name("path_collector")
            .spawn_blocking(move || {
                logger.with(|| {
                    let time = Instant::now();
                    let response = config_clone
                        .paths()
                        .iter()
                        .flat_map(|item| {
                            let root = FIMWatcher::glob_root(item.to_str().unwrap());
                            debug!(
                                "Checking path {} and found root {}",
                                item.display(),
                                root.display(),
                            );
                            if root == item.to_owned() {
                                vec![root]
                            } else {
                                let glob = GlobSetBuilder::new()
                                    .add(Glob::new(item.to_str().unwrap()).unwrap())
                                    .build()
                                    .unwrap();
                                // Use `WalkBuilder` to traverse the directory
                                WalkBuilder::new(root)
                                    .hidden(false) // Include hidden files
                                    .max_depth(Some(3))
                                    .build()
                                    .take_while(|_| is_system_running())
                                    .filter_map(Result::ok) // Skip any errors
                                    .filter(move |entry| glob.clone().is_match(entry.path()))
                                    .map(|entry| entry.path().to_owned())
                                    .collect::<Vec<PathBuf>>()
                            }
                        })
                        .collect::<Vec<PathBuf>>();
                    debug!("Took total {:?} time to collect paths", time.elapsed());
                    response
                })
            })
            .unwrap()
            .await
    }
}

#[async_trait]
impl Watcher for FIMWatcher {
    async fn watch(&self) -> anyhow::Result<(), FIMError> {
        async {
            let mut shutdown_receiver = self.stop_signal_receiver.lock().await;

            let paths_to_watch = self.collect_paths().await;

            if let Err(error) = paths_to_watch {
                error!(?error, "Failed to collect path to watch");
                shutdown_receiver.recv().await.ok();
                return Err(FIMError::FailedToCollectPath(error));
            }

            let paths_to_watch = paths_to_watch.unwrap();

            debug!("collected paths {:?}", paths_to_watch);

            if paths_to_watch.len() == 0 {
                shutdown_receiver.recv().await.ok();
                return Ok(());
            }

            info!("Starting watcher for config {}", self.config.category());

            let (notify_tx, tokio_rx) = unbounded_channel();

            let mut stream_task = self.start_event_receiver(tokio_rx);

            let mut watcher = match new_debouncer(
                Duration::from_secs(1),
                None,
                move |res: DebounceEventResult| match res {
                    Ok(events) => {
                        match notify_tx.send(events) {
                            Ok(_) => trace!("Send Event to tokio notifier"),
                            Err(error) => {
                                error!(?error, "Failed to publish event to tokio notifier");
                            }
                        };
                    }

                    Err(e) => error!("Notify Errors: {:?}", e),
                },
            ) {
                Ok(watcher) => watcher,
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to initialise watcher for config {:?}", self.config
                    );
                    shutdown_receiver.recv().await.ok();
                    return Err(error.into());
                }
            };

            for path in paths_to_watch.iter() {
                debug!("Watching path {}", path.display());
                match watcher.watch(path, RecursiveMode::Recursive) {
                    Ok(_) => {
                        debug!("Started watching path {}", path.display());
                    }
                    Err(error) => {
                        error!(?error, "Failed to watch path {}", path.display());
                    }
                }
            }

            tokio::select! {
                biased;

                _ = shutdown_receiver.recv() => {
                    info!("Stopping Watcher started for config {}", self.config.category());
                    watcher.stop();
                },

                _ = &mut stream_task => {
                    info!("Event Receiver task is finished executing");
                }
            };

            info!(
                "Shutting down file watcher for config {}",
                self.config.category()
            );

            Ok(())
        }
        .with_subscriber(self.logger.subscriber())
        .await
    }
}
