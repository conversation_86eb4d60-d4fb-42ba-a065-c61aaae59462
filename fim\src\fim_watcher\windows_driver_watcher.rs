use crate::{fim_event::<PERSON><PERSON><PERSON><PERSON>, Config<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Watcher};
use api::data_collection::send_fim_data;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use database::{
    models::{FIMConfig, FIMConfigType},
    Model, Uuid,
};
use globset::{Glob, GlobSetBuilder};
use ignore::WalkBuilder;
use logger::{debug, error, info, trace, ModuleLogger, WithSubscriber};
use serde_json::json;
use std::{
    collections::{HashMap, HashSet},
    env::current_exe,
    path::{Path, PathBuf},
    sync::Arc,
    time::{SystemTime, UNIX_EPOCH},
};
use tokio::{
    fs::File,
    sync::{
        broadcast::Receiver as BroadcastReceiver,
        mpsc::{channel, Receiver, Sender},
        <PERSON>tex, Semaph<PERSON>,
    },
    task::{Join<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>},
    time::Instant,
};
use tokio_stream::{wrappers::ReceiverStream, StreamExt};
use utils::{cpu::max_blocking_threads, dir::get_log_dir, shutdown::is_system_running};
use windows_driver_handler::{expand_registry_path, WinDriverEvent, WinDriverHandler};

static DRIVER_NAME: &str = "ZirozenDrv";
static FILE_NAME: &str = "\\\\.\\ZirozenDev";
static PORT_NAME: &str = "\\ZirozenPort";

#[derive(Debug, Clone)]
pub struct FIMDriverEvent {
    event: WinDriverEvent,
    config: FIMConfig,
}

impl FIMDriverEvent {
    pub fn new(event: WinDriverEvent, config: FIMConfig) -> Self {
        Self { event, config }
    }

    pub fn build_fim_event(&self) -> FIMEvent {
        let mut fs_event = if self.event.is_registry_event() {
            FIMEvent::build_registry_event_from_win_driver(self.event.clone(), &self.config)
        } else {
            FIMEvent::build_file_event_from_win_driver(self.event.clone(), &self.config)
        };
        fs_event.generate_id();
        fs_event
    }
}

pub struct FIMWatcher {
    logger: ModuleLogger,
    config: Arc<Vec<FIMConfig>>,
    stop_signal_receiver: Arc<Mutex<BroadcastReceiver<bool>>>,
    endpoint_id: i64,
}

impl FIMWatcher {
    pub fn glob_root(pattern: &str) -> PathBuf {
        let path = Path::new(pattern);

        // Find the first component that contains a glob character
        let mut root = PathBuf::new();
        for component in path.components() {
            let comp_str = component.as_os_str().to_string_lossy();
            if comp_str.contains('*')
                || comp_str.contains('?')
                || comp_str.contains('[')
                || comp_str.contains('{')
            {
                break; // Stop at the first wildcard
            }
            root.push(component);
        }

        // Default to current directory if no root found
        if root.as_os_str().is_empty() {
            PathBuf::from(".")
        } else {
            root
        }
    }

    pub fn new(
        config: Vec<FIMConfig>,
        endpoint_id: i64,
        stop_signal_receiver: BroadcastReceiver<bool>,
    ) -> Self {
        Self {
            logger: ModuleLogger::new(
                "fim",
                None,
                Some(
                    get_log_dir()
                        .join("fim/windows-driver".to_owned())
                        .to_string_lossy()
                        .to_string(),
                ),
            ),
            config: Arc::new(
                config
                    .into_iter()
                    .map(|mut config| {
                        if config.config_type == FIMConfigType::Registry {
                            config.include_path = config
                                .include_path
                                .into_iter()
                                .map(|path| {
                                    PathBuf::from(expand_registry_path(
                                        path.to_string_lossy().to_string().as_str(),
                                    ))
                                })
                                .collect();
                            config.exclude_path = config
                                .exclude_path
                                .into_iter()
                                .map(|path| {
                                    PathBuf::from(expand_registry_path(
                                        path.to_string_lossy().to_string().as_str(),
                                    ))
                                })
                                .collect();
                            config
                        } else {
                            config
                        }
                    })
                    .collect(),
            ),
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            endpoint_id,
        }
    }

    fn remove_old_entries(map: &mut HashMap<String, i64>) {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards")
            .as_secs() as i64;

        map.retain(|_, &mut timestamp| now.saturating_sub(timestamp) <= 5);
    }

    fn start_event_receiver(
        &self,
        rx: Receiver<WinDriverEvent>,
        event_processor: Sender<FIMDriverEvent>,
        paths_to_watch: HashMap<i64, Vec<PathBuf>>,
        registry_to_watch: HashMap<i64, Vec<PathBuf>>,
        config_id_map: HashMap<i64, FIMConfig>,
    ) -> JoinHandle<()> {
        tokio::task::spawn(
            async move {
                let mut excluded_processes = HashSet::new();

                excluded_processes.insert(current_exe().unwrap_or_default().to_string_lossy().to_string());

                debug!("Excluded processes: {:?}", excluded_processes);

                let mut stream = ReceiverStream::new(rx);

                let mut debounced_event_map = HashMap::new();

                while let Some(event) = stream.next().await.take_if(|_| is_system_running()) {
                    if event.is_process_event() {
                        continue;
                    } else if event.is_registry_event() {
                        trace!("Received Registry event {:?}", event);
                        let target_path =
                            PathBuf::from(&event.get_registry_data().as_ref().unwrap().key_name);

                        let related_configs = registry_to_watch
                            .iter()
                            .filter(|item| {
                                item.1.iter().filter(|p| target_path.starts_with(p)).count() > 0
                            })
                            .map(|item| item.0.to_owned())
                            .collect::<Vec<i64>>();

                        // for now considering only first config
                        if let Some(config) = related_configs.first() {
                            if let Some(config) = config_id_map.get(&config) {
                                if let Err(error)  = event_processor.send(FIMDriverEvent::new(event, config.clone())).await {
                                    error!(?error, "Failed to send event to processor task");
                                }
                            } else {
                                error!("Failed to find config with id {} when resolving which config to apply event", config);
                            }
                        }
                        continue;
                    }
                    let should_process = match debounced_event_map.insert(event.unique_key(), event.timestamp()) {
                        Some(timestamp) => {
                            if event.timestamp() - timestamp > 5 {
                                // 5sec passed let's create new event
                                true
                            } else {
                                false
                            }
                        },
                        None => {
                            true
                        },
                    };
                    trace!("Received File event {:?}", event);
                    if event.get_file_data().is_some_and(|file_data| excluded_processes.contains(file_data.proc_info.get_name()) == false) && should_process {
                        let target_path =
                            PathBuf::from(&event.get_file_data().as_ref().unwrap().file_name);

                        let related_configs = paths_to_watch
                            .iter()
                            .filter(|item| {
                                item.1.iter().filter(|p| target_path.starts_with(p)).count() > 0
                            })
                            .map(|item| item.0.to_owned())
                            .collect::<Vec<i64>>();

                        // for now considering only first config
                        if let Some(config) = related_configs.first() {
                            if let Some(config) = config_id_map.get(&config) {
                                if let Err(error)  = event_processor.send(FIMDriverEvent::new(event, config.clone())).await {
                                    error!(?error, "Failed to send event to processor task");
                                }
                            } else {
                                error!("Failed to find config with id {} when resolving which config to apply event", config);
                            }
                        }
                    }
                    FIMWatcher::remove_old_entries(&mut debounced_event_map);
                }
                debug!("Finished Windows Driver Event Receiver Task");
            }
            .with_subscriber(self.logger.subscriber()),
        )
    }

    fn start_event_processor(&self, mut rx: Receiver<FIMDriverEvent>) -> JoinHandle<()> {
        let logger = self.logger.clone();
        let semaphore = Semaphore::new(max_blocking_threads() - 4);
        tokio::task::spawn(
            async move {
                while let Some(event) = rx.recv().await {
                    let _permit = semaphore.acquire().await;
                    let logger_clone = logger.clone();
                    let (fim_event, event) = match tokio::task::spawn_blocking(move || {
                        logger_clone.with(|| (event.build_fim_event(), event))
                    })
                    .await
                    {
                        Ok(event) => event,
                        Err(error) => {
                            error!(?error, "Failed to join hash compute task for event");
                            continue;
                        }
                    };
                    debug!("FIM Event: {:?}", fim_event);
                    if event.config.config_type == FIMConfigType::ConfigFile {
                        ConfigFileWatcher::sync_event(&event.config, fim_event, logger.clone())
                            .await;
                    } else {
                        match fim_event.persist().await {
                            Err(error) => {
                                error!(?error, "Failed to persist event {:?}", event);
                            }
                            _ => {}
                        };
                    }
                }
                debug!("Finished Windows Driver Event Processor Task");
            }
            .with_subscriber(self.logger.subscriber()),
        )
    }

    async fn collect_paths(&self, paths: Vec<PathBuf>) -> Result<Vec<PathBuf>, JoinError> {
        let logger = self.logger.clone();
        debug!("Calculating paths to watch from glob patterns");
        tokio::task::Builder::new()
            .name("path_collector")
            .spawn_blocking(move || {
                logger.with(|| {
                    let time = Instant::now();
                    let response = paths
                        .iter()
                        .flat_map(|item| {
                            let root = FIMWatcher::glob_root(item.to_str().unwrap());
                            debug!(
                                "Checking path {} and found root {}",
                                item.display(),
                                root.display(),
                            );
                            if root == item.to_owned() {
                                vec![root]
                            } else {
                                let glob = GlobSetBuilder::new()
                                    .add(Glob::new(item.to_str().unwrap()).unwrap())
                                    .build()
                                    .unwrap();
                                // Use `WalkBuilder` to traverse the directory
                                WalkBuilder::new(root)
                                    .hidden(false) // Include hidden files
                                    .max_depth(Some(3))
                                    .build()
                                    .filter_map(Result::ok) // Skip any errors
                                    .filter(move |entry| glob.clone().is_match(entry.path()))
                                    .map(|entry| entry.path().to_owned())
                                    .collect::<Vec<PathBuf>>()
                            }
                        })
                        .collect::<Vec<PathBuf>>();
                    debug!("Took total {:?} time to collect paths", time.elapsed());
                    response
                })
            })
            .unwrap()
            .await
    }

    async fn config_file_initialization(&self, path: &PathBuf, config: &FIMConfig) {
        let mut event = FIMEvent::default();
        event.set_target_path(path.to_string_lossy().to_string());
        event.generate_hash();

        if ConfigFileWatcher::should_send_file(&event, self.logger.clone()).await {
            if let Some(file) = ConfigFileWatcher::send_file(&event).await {
                let event_time = match File::open(path).await {
                    Ok(f) => match f.metadata().await {
                        Ok(metadata) => {
                            Into::<DateTime<Utc>>::into(metadata.created().unwrap()).timestamp()
                        }
                        Err(error) => {
                            error!(?error, "Failed to open file metadata {}", path.display());
                            chrono::Utc::now().timestamp()
                        }
                    },
                    Err(error) => {
                        error!(?error, "Failed to open file {}", path.display());
                        chrono::Utc::now().timestamp()
                    }
                };
                // here send first time file change api
                match send_fim_data(json!({
                    "asset_id" : self.endpoint_id,
                    "data" : {
                        "fim_config_file" : {
                            "file_name" : file.file_name,
                            "file_path" : path.to_string_lossy().to_string(),
                            "event_id" : Uuid::new_v4().to_string(),
                            "event_time" : event_time,
                            "config_id" : config.id(),
                            "ref_name" : file.file_ref
                    }
                  }
                }))
                .await
                {
                    Ok(_) => debug!("Sent first time file change api"),
                    Err(error) => error!(?error, "Failed to send first time file change api"),
                };
            }
        }
    }
}

#[async_trait]
impl Watcher for FIMWatcher {
    async fn watch(&self) -> anyhow::Result<(), FIMError> {
        async {
            info!("---------------------- Starting FIM Windows Driver ------------------------");

            let mut shutdown_receiver = self.stop_signal_receiver.lock().await;

            let mut paths_to_watch = HashMap::new();
            let mut paths_to_ignore = HashMap::new();
            let mut registry_to_watch = HashMap::new();
            let mut registry_to_ignore = HashMap::new();

            let mut config_id_map = HashMap::new();

            for config in self.config.iter() {
                config_id_map.insert(config.id(), config.clone());
                if config.config_type == FIMConfigType::ConfigFile {
                    if let Some(path) = config.paths().first() {
                        if !path.exists() {
                            error!(
                                "Config file not found at path {}",
                                path.to_string_lossy().to_string()
                            );
                        } else {
                            paths_to_watch.insert(
                                config.id(),
                                vec![path.parent().unwrap_or_else(|| path).to_path_buf()],
                            );
                            self.config_file_initialization(path, config).await;
                        }
                    } else {
                        error!("No Config file path is given {:?}", config);
                    }
                } else if config.config_type == FIMConfigType::Registry {
                    registry_to_watch.insert(
                        config.id(),
                        config
                            .paths()
                            .iter()
                            .map(|item| {
                                PathBuf::from(expand_registry_path(
                                    item.to_string_lossy().to_string().as_str(),
                                ))
                            })
                            .collect::<Vec<PathBuf>>(),
                    );
                    registry_to_ignore.insert(
                        config.id(),
                        config
                            .excluded_paths()
                            .iter()
                            .map(|item| {
                                PathBuf::from(expand_registry_path(
                                    item.to_string_lossy().to_string().as_str(),
                                ))
                            })
                            .collect::<Vec<PathBuf>>(),
                    );
                } else {
                    match self.collect_paths(config.paths().clone()).await {
                        Ok(paths) => {
                            paths_to_watch.insert(config.id(), paths);
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to collect path for config {}",
                                config.category()
                            );
                        }
                    };
                    match self.collect_paths(config.excluded_paths().clone()).await {
                        Ok(paths) => {
                            if paths.len() > 0 {
                                paths_to_ignore.insert(config.id(), paths);
                            }
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to collect exclude path for config {}",
                                config.category()
                            );
                        }
                    };
                }
            }

            debug!("collected paths {:?}", paths_to_watch);
            debug!("collected registries {:?}", registry_to_watch);

            if paths_to_watch.len() == 0 && registry_to_watch.len() == 0 {
                info!("No config is found to watch");
                shutdown_receiver.recv().await.ok();
                return Ok(());
            }

            // load driver

            if let Err(error) = WinDriverHandler::load(DRIVER_NAME) {
                error!(?error, "Failed to load driver");
                shutdown_receiver.recv().await.ok();
                return Err(error.into());
            }

            let (tx, rx) = channel(10);

            let driver_handler = WinDriverHandler::new(DRIVER_NAME, FILE_NAME, PORT_NAME, tx);

            if let Err(error) = driver_handler.unwatch_folder("") {
                error!(?error, "Failed to clear all folder from driver watch list");
            } else {
                debug!("Cleared all file system paths from driver list");
            }
            if let Err(error) = driver_handler.unwatch_registry("") {
                error!(
                    ?error,
                    "Failed to clear all registry paths from driver watch list"
                );
            } else {
                debug!("Cleared all registry paths from driver list");
            }

            // watch file paths
            for (id, paths) in paths_to_watch.iter() {
                info!("Processing include paths for config {}", id);
                for path in paths.iter() {
                    match driver_handler.watch_folder(path) {
                        Ok(()) => {
                            debug!("Watching path {}", path.display());
                        }
                        Err(error) => {
                            error!(?error, "Failed to watch path {}", path.display());
                        }
                    };
                }
            }

            // ignore file paths
            for (id, paths) in paths_to_ignore.iter() {
                info!("Processing exclude paths for config {}", id);
                for path in paths.iter() {
                    match driver_handler.exclude_folder(path) {
                        Ok(()) => {
                            debug!("Excluded path {}", path.display());
                        }
                        Err(error) => {
                            error!(?error, "Failed to exclude path {}", path.display());
                        }
                    };
                }
            }

            // watch registry
            for (id, paths) in registry_to_watch.iter() {
                info!("Processing include registry for config {}", id);
                for path in paths.iter() {
                    match driver_handler.watch_registry(path) {
                        Ok(()) => {
                            debug!("Watching registry at {}", path.display());
                        }
                        Err(error) => {
                            error!(?error, "Failed to watch registry {}", path.display());
                        }
                    };
                }
            }

            // ignore registry paths
            for (id, paths) in paths_to_ignore.iter() {
                info!("Processing exclude registry for config {}", id);
                for path in paths.iter() {
                    match driver_handler.exclude_registry(path) {
                        Ok(()) => {
                            debug!("Excluded registry {}", path.display());
                        }
                        Err(error) => {
                            error!(?error, "Failed to exclude registry at {}", path.display());
                        }
                    };
                }
            }

            let driver_controller = match driver_handler.start() {
                Ok(controller) => controller,
                Err(error) => {
                    error!(?error, "Failed to start windows driver handler");
                    shutdown_receiver.recv().await.ok();
                    return Ok(());
                }
            };

            let (event_processor_tx, event_processor_rx) = channel(10);
            let mut event_receiver_task = self.start_event_receiver(
                rx,
                event_processor_tx,
                paths_to_watch,
                registry_to_watch,
                config_id_map,
            );
            let mut event_processor_task = self.start_event_processor(event_processor_rx);

            tokio::select! {
                biased;

                _ = shutdown_receiver.recv() => {
                    debug!("Windows driver watcher received shutdown event");
                },

                result = &mut event_receiver_task => {
                    match result {
                        Ok(()) => {
                            debug!("Windows Driver event receiver has finished execution");
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to join windows driver event receiver thread"
                            );
                        }
                    }
                },

                result = &mut event_processor_task => {
                    match result {
                        Ok(()) => {
                            debug!("Windows Driver event processor task has finished execution");
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to join windows driver event processor task"
                            );
                        }
                    }
                }
            }

            driver_controller.stop();
            if let Err(error) = WinDriverHandler::unload(DRIVER_NAME) {
                error!(?error, "Failed to unload driver");
            } else {
                debug!("Unloaded driver successfully");
            }
            debug!("Stopped Windows FIM Watcher");
            Ok(())
        }
        .with_subscriber(self.logger.subscriber())
        .await
    }
}
