use crate::tasks::attachment_downloader::AttachmentDownloader;
use crate::tasks::patch_scan::windows::windows_missing_confirm_with_script::ConfirmMissingPatchWithScript;
use crate::tasks::unzip::Unzip;
use crate::TaskExecutable;
use anyhow::Result;
use api::patch::{
    get_missing_uuids_with_superseeded_removed, get_windows_xml_files, send_patch_discovered_data,
};
use csv_async::AsyncReaderBuilder;
use dashmap::DashMap;
use database::models::FileAttachment;
use database::Model;
use futures::stream::{self as stream, StreamExt};
use logger::{debug, error, info, ModuleLogger};
use rayon::iter::{IndexedParallelIterator, IntoParallelIterator, ParallelIterator};
use serde::Deserialize;
use serde_json::{json, Value};
use shell::ShellCommand;
use std::sync::Arc;
use std::time::Duration;
use tokio::fs::{self, File};
use tokio::time::Instant;
use utils::cpu::max_blocking_threads;
use utils::dir::{get_log_dir, get_patch_cab_dir, get_patch_dir};
use utils::file::get_extension;
use utils::serde::tags_deserialize;
use utils::shutdown::is_system_running;
use windows::core::BSTR;
use windows::Win32::Foundation::VARIANT_TRUE;
use windows::Win32::System::{
    Com::{CoCreateInstance, CoInitializeEx, CLSCTX_INPROC_SERVER, COINIT_MULTITHREADED},
    UpdateAgent::{IUpdate, IUpdateSearcher, UpdateSearcher},
};
use windows_patch_xml_checker::{
    PatchStatus, PersistScanResult, WindowsUpdate, WindowsXmlCheckerError,
};

#[derive(Debug, Clone, Deserialize, Default)]
pub(crate) struct CsvRecord {
    pub uuid: String,
    pub category: String,
    #[serde(rename = "timestamp")]
    pub timestamp: Option<u64>,
    #[serde(deserialize_with = "tags_deserialize")]
    kb_ids: Vec<String>,
}

#[derive(Debug, Clone)]
pub(crate) struct PatchFile {
    pub csv_record: CsvRecord,
    pub evaluation_result: Option<PatchStatus>,
}

pub struct WindowsPatchFinder<'a> {
    files: Vec<PatchFile>,
    attachments: Vec<FileAttachment>,
    endpoint_id: i64,
    update_history: Arc<String>,
    wmic_qfe_output: Arc<String>,
    scan_output: Arc<String>,
    result_cache: Arc<DashMap<String, u8>>,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> WindowsPatchFinder<'a> {
    pub fn new(endpoint_id: i64, scan_output: String, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self {
            files: vec![],
            attachments: vec![],
            endpoint_id,
            scan_output: Arc::new(scan_output),
            wmic_qfe_output: Arc::new(WindowsPatchFinder::get_wmic_qfe_output()),
            update_history: Arc::new(WindowsPatchFinder::get_update_history()),
            result_cache: Arc::new(DashMap::new()),
            task,
        }
    }

    fn get_wmic_qfe_output() -> String {
        match futures::executor::block_on(async {
            ShellCommand::new("wmic qfe list full /format:table")
                .run()
                .await
        }) {
            Ok(output) => {
                debug!("Got wmic qfe output {}", output.output);
                output.output
            }
            Err(error) => {
                error!(?error, "Failed to execute wmic qfe command");
                "".to_owned()
            }
        }
    }

    fn extract_info_from_update(entry: IUpdate) -> String {
        unsafe {
            let uuid = match entry.Identity() {
                Ok(update_id) => update_id.UpdateID().unwrap_or(BSTR::new()).to_string(),
                Err(error) => {
                    error!(?error, "Failed to get update id of installed update");
                    "".to_owned()
                }
            };
            let kb_ids = match entry.KBArticleIDs() {
                Ok(kb_ids) => {
                    let mut kbs = vec![];
                    for j in 0..kb_ids.Count().unwrap_or_default() {
                        if let Ok(kb) = kb_ids.get_Item(j) {
                            kbs.push(format!("KB{}", kb.to_string()));
                        }
                    }
                    kbs
                }
                Err(error) => {
                    error!(?error, "Failed to get kb ids");
                    vec![]
                }
            };
            let title = match entry.Title() {
                Ok(title) => title.to_string(),
                Err(error) => {
                    error!(?error, "Failed to get title from update entry");
                    "".to_owned()
                }
            };
            format!("{}: {}: {}", uuid.to_lowercase(), title, kb_ids.join(","))
        }
    }

    fn get_update_history() -> String {
        let mut update_history = vec![];
        // Initialize COM
        unsafe {
            let result = CoInitializeEx(None, COINIT_MULTITHREADED).ok();
            if result.is_err() {
                error!("Failed to initialize com binding {:?}", result.err());
                return update_history.join("\n");
            }
        };

        // Create the UpdateSearcher instance
        let searcher: IUpdateSearcher = unsafe {
            match CoCreateInstance(&UpdateSearcher, None, CLSCTX_INPROC_SERVER) {
                Ok(searcher) => searcher,
                Err(error) => {
                    error!(?error, "Failed to initialize searcher");
                    return update_history.join("\n");
                }
            }
        };

        // Get total number of updates in history
        // let history_count: i32 = unsafe {
        //     match searcher.GetTotalHistoryCount() {
        //         Ok(count) => count,
        //         Err(error) => {
        //             error!(
        //                 ?error,
        //                 "Failed to get update installed counts using searcher"
        //             );
        //             return update_history.join("\n");
        //         }
        //     }
        // };
        // debug!("Found Total Installed Update Count: {}", history_count);

        // // Iterate through update history
        // for i in 0..history_count {
        //     unsafe {
        //         match searcher.QueryHistory(i, 1) {
        //             Ok(entries) => {
        //                 match entries.get_Item(0) {
        //                     Ok(entry) => {
        //                         let history =
        //                             WindowsPatchFinder::extract_info_from_update_history_entry(
        //                                 entry,
        //                             );
        //                         debug!("Got update entry: {}", history);
        //                         update_history.push(history);
        //                     }
        //                     Err(error) => {
        //                         error!(?error, "Failed to get update entry");
        //                     }
        //                 };
        //             }
        //             Err(error) => {
        //                 error!(?error, "Failed to get windows update entry at index {}", i);
        //             }
        //         }
        //     };
        // }
        // let history = update_history.join("\n");
        // debug!("Got windows update history: \n{}", history);
        unsafe {
            if let Err(error) = searcher.SetIncludePotentiallySupersededUpdates(VARIANT_TRUE) {
                error!(?error, "Failed to set include superseeded updates");
            }
            match searcher.Search(&BSTR::from("IsInstalled=1")) {
                Ok(result) => match result.Updates() {
                    Ok(collection) => {
                        let count = collection.Count().unwrap_or(0);
                        logger::debug!("Found IsInstalled=1 count: {}", count);
                        for i in 0..count {
                            match collection.get_Item(i) {
                                Ok(update) => {
                                    let history =
                                        WindowsPatchFinder::extract_info_from_update(update);
                                    debug!("Got update entry: {}", history);
                                    update_history.push(history);
                                }
                                Err(error) => {
                                    error!(?error, "Failed to get item of update entry");
                                }
                            }
                        }
                        logger::debug!("update searcher is done.");
                    }
                    Err(error) => {
                        logger::error!(
                            ?error,
                            "Failed to get update collection from search result"
                        );
                    }
                },
                Err(error) => {
                    logger::error!(?error, "Failed to search installed updates");
                }
            }
        }
        let history = update_history.join("\n");
        debug!("Got windows update history: \n{}", history);
        history
    }

    async fn collect_files(&self) -> Vec<PatchFile> {
        let mut files = vec![];
        let mut counter = 1;
        for attachment in &self.attachments {
            debug!("Collecting entries for {}", attachment.real_name);
            let txt_file_path = get_patch_dir().join(format!("{}.txt", attachment.real_name));

            match File::open(&txt_file_path).await {
                Ok(file) => {
                    let mut rdr = AsyncReaderBuilder::new()
                        .has_headers(false)
                        .create_deserializer(file);
                    let mut records = rdr.deserialize::<CsvRecord>();
                    while let Some(record) = records.next().await {
                        match record {
                            Ok(record) => {
                                files.push(PatchFile {
                                    evaluation_result: None,
                                    csv_record: record,
                                });
                                counter = counter + 1;
                            }
                            Err(error) => {
                                error!(?error, "Failed to read csv record");
                            }
                        }
                    }
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to open txt file {}",
                        txt_file_path.display()
                    );
                    continue;
                }
            }
        }
        files
    }

    fn process_single_file(
        mut file: PatchFile,
        scan_output: Arc<String>,
        update_history: Arc<String>,
        wmic_qfe_output: Arc<String>,
        result_cache: Arc<DashMap<String, u8>>,
    ) -> PatchFile {
        ModuleLogger::new(
            "windows_patch_xml_checker",
            Some(get_log_dir().join("windows_patch_scan").to_path_buf()),
            Some(file.csv_record.uuid.clone()),
        )
        .with(move || {
            let csv_record = file.csv_record.clone();
            let result = match WindowsUpdate::new()
                .level(1)
                .uuid(csv_record.uuid)
                .timestamp(csv_record.timestamp.unwrap_or_default())
                .with_result_cache(result_cache)
                .scanned_output(scan_output)
                .update_history(update_history)
                .wmic_qfe_output(wmic_qfe_output)
                .category(csv_record.category)
                .sub_kb(
                    csv_record
                        .kb_ids
                        .into_iter()
                        .filter(|kb| kb.is_empty() == false)
                        .collect(),
                )
                .parse()
            {
                Ok(update_package) => {
                    file.evaluation_result = Some(update_package.evaluate());
                    file
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to build windows update package from xml for uuid {}",
                        file.csv_record.uuid
                    );
                    file.evaluation_result = Some(match error {
                        WindowsXmlCheckerError::XmlParsingError(_) => PatchStatus::InvalidFile,
                        WindowsXmlCheckerError::FileNotFoundError(_) => PatchStatus::FileMissing,
                        _ => PatchStatus::Unknown,
                    });
                    file
                }
            };
            // just to cool down cpu
            std::thread::sleep(Duration::from_millis(500));
            result
        })
    }

    async fn confirm_missing_uuids_with_script(uuid: &str, task: Box<&dyn TaskExecutable>) -> bool {
        let attachment = match AttachmentDownloader::new(
            FileAttachment {
                real_name: format!("{}.7z", uuid),
                ref_name: format!("{}.7z", uuid),
                zirozen_download_url: Some(format!("/patch/asset/download-cab-file/{}", uuid)),
                ..Default::default()
            },
            task.clone(),
            Some(get_patch_cab_dir()),
        )
        .download()
        .await
        {
            Ok(attachment) => attachment,
            Err(_) => {
                return true;
            }
        };
        match Unzip::new(&attachment, task)
            .extract(Some(attachment.disk_local_path().join(uuid)))
            .await
        {
            Ok(_) => {
                debug!("Successfully extracted cab file");
                if let Some(result) = ConfirmMissingPatchWithScript::new(uuid).verify().await {
                    result
                } else {
                    true
                }
            }
            Err(_) => return true,
        }
    }

    async fn process_files(self) -> Result<(Vec<Value>, Vec<Value>)> {
        let time = Instant::now();
        let files = self.files;
        let total_files = files.len();

        info!("Processing total {} files", total_files);

        let mut windows_missing_uuids: Vec<String> = vec![];
        let mut final_missing_uuids: Vec<Value> = vec![];
        let mut installed_uuids: Vec<Value> = vec![];

        match PersistScanResult::default().get_all(None).await {
            Ok(results) => {
                for result in results {
                    self.result_cache
                        .insert(result.uuid.to_string(), result.status.clone().into());
                }
            }
            Err(error) => {
                error!(?error, "Failed to get patch saved results");
            }
        };

        let allowed_threads = max_blocking_threads();

        debug!("Using {} cores for rayon thread pool", allowed_threads);

        let logger = self.task.get_logger().clone();

        let pool = rayon::ThreadPoolBuilder::new()
            .num_threads(allowed_threads)
            .build()
            .unwrap();

        debug!("Existing Cache {:?}", self.result_cache);

        let result = pool.install(|| {
            let result = files
                .into_par_iter()
                .enumerate()
                .take_any_while(|_| is_system_running())
                .map(|(index, mut file)| {
                    logger.with(|| {
                        let result = if let Some(cached_value) = self.result_cache.get(&file.csv_record.uuid.to_lowercase()) {
                            let patch_status: PatchStatus = cached_value.clone().into();
                            debug!(
                                "Skipping file [{} of {}] with uuid {} as it is already processed and saved as not applicable and timestamp is not changed",
                                index,
                                total_files,
                                file.csv_record.uuid
                            );
                            file.evaluation_result = Some(patch_status);
                            file
                        } else {
                            WindowsPatchFinder::process_single_file(
                                file,
                                self.scan_output.clone(),
                                self.update_history.clone(),
                                self.wmic_qfe_output.clone(),
                                self.result_cache.clone(),
                            )
                        };

                        info!(
                            "Files [{} of {}] is finished processing with id {} with status {}",
                            index + 1,
                            total_files,
                            result.csv_record.uuid,
                            result.evaluation_result.as_ref().unwrap()
                        );
                        result
                    })
                })
                .collect::<Vec<PatchFile>>();

            result
        });

        if !is_system_running() {
            info!("Quitting windows patch scanning as system is shutting down");
            return Ok((vec![], vec![]));
        }

        for file in result {
            if let Some(status) = file.evaluation_result {
                if status.is_missing() {
                    windows_missing_uuids.push(file.csv_record.uuid);
                } else if status.is_installed() {
                    installed_uuids.push(json!({
                        "uuid": file.csv_record.uuid,
                    }));
                }
            }
        }

        if windows_missing_uuids.len() > 0 {
            match get_missing_uuids_with_superseeded_removed(&windows_missing_uuids).await {
                Ok(uuids) => {
                    debug!(
                            "Received {} of total {} missing {:?} uuids which are missing after removing superseeded patches",
                            uuids.len(), windows_missing_uuids.len(), uuids
                        );

                    if uuids.len() > 0 {
                        if get_patch_cab_dir().exists() == false {
                            match fs::create_dir_all(get_patch_cab_dir()).await {
                                Ok(_) => debug!("Created patch cab file saving directory"),
                                Err(error) => {
                                    error!(?error, "Failed to create directory to save cab files")
                                }
                            }
                        }
                        let mut uuid_stream = stream::iter(uuids);
                        while let Some((uuid, has_cab_file)) = uuid_stream.next().await {
                            if has_cab_file {
                                if WindowsPatchFinder::confirm_missing_uuids_with_script(
                                    &uuid,
                                    self.task.clone(),
                                )
                                .await
                                {
                                    final_missing_uuids.push(json!({"uuid": uuid}))
                                }
                            } else {
                                final_missing_uuids.push(json!({"uuid": uuid}))
                            }
                        }
                    }
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to remove superseeded uuids from api so sending all uuids"
                    );
                    windows_missing_uuids
                        .into_iter()
                        .for_each(|uuid| final_missing_uuids.push(json!({"uuid": uuid})));
                }
            }
        }
        debug!("Time taken for xml scanning {:?}", time.elapsed());
        Ok((final_missing_uuids, installed_uuids))
    }

    async fn download_and_extract_attachments(&self) -> Result<Vec<FileAttachment>> {
        let mut attachments = vec![];
        for (file_stem, ref_name) in get_windows_xml_files(self.endpoint_id as u32)
            .await?
            .into_iter()
        {
            let file_name = format!("{}.{}", file_stem, get_extension(&ref_name));

            let download_url =
                format!("/patch/asset/download-applicability-rule-file/{}", ref_name).to_owned();

            let attachment = match AttachmentDownloader::new(
                FileAttachment {
                    real_name: file_name.clone(),
                    ref_name: ref_name.clone(),
                    zirozen_download_url: Some(download_url),
                    ..Default::default()
                },
                self.task.clone(),
                Some(get_patch_dir()),
            )
            .download()
            .await
            {
                Ok(attachment) => attachment,
                Err(error) => {
                    error!(?error, "Failed to download file {} with error", ref_name);
                    continue;
                }
            };

            if Unzip::new(&attachment, self.task.clone())
                .extract(None)
                .await
                .is_ok()
            {
                debug!(
                    "Patch {} 7z is extracted successfully",
                    attachment.real_name
                );

                let patch_directory = get_patch_dir();

                // to cool down cpu
                tokio::time::sleep(Duration::from_millis(10)).await;

                if patch_directory.join("allpatchlist.txt").exists() {
                    if let Err(error) = fs::rename(
                        patch_directory.join("allpatchlist.txt"),
                        patch_directory.join(format!("{}.txt", attachment.real_name)),
                    )
                    .await
                    {
                        error!(
                            ?error,
                            "Failed to rename allpatchlist.txt to {}.txt", attachment.real_name
                        )
                    }

                    attachments.push(attachment);
                }
            }
        }

        Ok(attachments)
    }

    pub async fn scan(mut self) -> Result<()> {
        let time = Instant::now();
        let patch_directory = get_patch_dir();

        fs::create_dir_all(&patch_directory).await?;

        self.attachments = match self.download_and_extract_attachments().await {
            Ok(attachments) => attachments,
            Err(error) => return Err(error.into()),
        };

        self.files = self.collect_files().await;

        let endpoint_id = self.endpoint_id;

        let (missing_uuids, installed_uuids) = self.process_files().await?;
        info!(
            "Total {} missing patches {}",
            missing_uuids.len(),
            serde_json::to_string(&missing_uuids)?
        );
        info!(
            "Total {} installed patches {}",
            installed_uuids.len(),
            serde_json::to_string(&installed_uuids)?
        );

        send_patch_discovered_data(
            endpoint_id,
            json!({
                "missingPatchList": missing_uuids,
                "installedPatchList": installed_uuids
            }),
        )
        .await?;

        debug!("Time taken for full scanning {:?}", time.elapsed());

        Ok(())
    }
}
