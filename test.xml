<upd:Update xmlns:pub="http://schemas.microsoft.com/msus/2002/12/Publishing"
  xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules"
  xmlns:lar="http://schemas.microsoft.com/msus/2002/12/LogicalApplicabilityRules"
  xmlns:upd="http://schemas.microsoft.com/msus/2002/12/Update">
  <upd:UpdateIdentity UpdateID="5671b1d0-eb3f-4259-b777-ae7aa53b51aa" RevisionNumber="209" />
  <upd:Properties DefaultPropertiesLanguage="en" UpdateType="Detectoid" ExplicitlyDeployable="false"
    PerUser="false" IsPublic="true" DetectoidType="SKU or Feature" MaxDownloadSize="0"
    MinDownloadSize="0" PublicationState="Published" CreationDate="2020-10-20T16:29:16.586Z"
    PublisherID="16bdcf76-ee28-449c-b94d-e2317d0496bd"></upd:Properties>
  <upd:Relationships>
    <upd:Prerequisites>
      <upd:AtLeastOne>
        <upd:UpdateIdentity UpdateID="a3c2375d-0c8a-42f9-bce0-28333e198407" />
        <upd:UpdateIdentity UpdateID="d2085b71-5f1f-43a9-880d-ed159016d5c6" />
        <upd:UpdateIdentity UpdateID="21210d67-50bc-4254-a695-281765e10665" />
        <upd:UpdateIdentity UpdateID="b3c75dc1-155f-4be4-b015-3f1a91758e52" />
        <upd:UpdateIdentity UpdateID="f702a48c-919b-45d6-9aef-ca4248d50397" />
        <upd:UpdateIdentity UpdateID="08db04e7-75f3-4c57-90ee-b6311de7f44b" />
      </upd:AtLeastOne>
    </upd:Prerequisites>
  </upd:Relationships>
  <upd:ApplicabilityRules>
    <upd:IsInstalled>
      <lar:Not xmlns:lar="http://schemas.microsoft.com/msus/2002/12/LogicalApplicabilityRules">
        <lar:And><!-- UpdateManagementGroup capability should be 'Full Management' available only on
          Pro, Enterprise. Core SKUs do not allow any WUfB deferrals -->
          <bar:LicenseDword Value="UpdatePolicy-UpdateManagementGroup" Comparison="EqualTo" Data="2"
            xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
          <lar:Not>
            <lar:Or><!-- Some editions do not have the UpdatePolicy-UpdateManagementGroup policy
              configured and it defaults to 2. --><!-- Working around that bug using edition specific check -->
              <bar:RegSz Value="EditionID" Comparison="EqualTo" Data="Core" Key="HKEY_LOCAL_MACHINE"
                Subkey="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegSz Value="EditionID" Comparison="EqualTo" Data="CoreCountrySpecific"
                Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegSz Value="EditionID" Comparison="EqualTo" Data="CoreN"
                Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegSz Value="EditionID" Comparison="EqualTo" Data="CoreSingleLanguage"
                Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegSz Value="EditionID" Comparison="EqualTo" Data="CoreARM"
                Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegSz Value="EditionID" Comparison="EqualTo" Data="CoreConnected"
                Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegSz Value="EditionID" Comparison="EqualTo" Data="CoreConnectedCountrySpecific"
                Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegSz Value="EditionID" Comparison="EqualTo" Data="CoreConnectedN"
                Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegSz Value="EditionID" Comparison="EqualTo" Data="CoreConnectedSingleLanguage"
                Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
            </lar:Or>
          </lar:Not>
          <lar:Or>
            <lar:And><!-- Applicable only if the WindowsVersion is between 10.0.0.10586 to
              10.0.18363.* -->
              <bar:WindowsVersion Comparison="GreaterThanOrEqualTo" BuildNumber="15063"
                MajorVersion="10" MinorVersion="0" ProductType="1"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:WindowsVersion Comparison="LessThanOrEqualTo" BuildNumber="18363"
                MajorVersion="10" MinorVersion="0" ProductType="1"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegDword Value="DeferFeatureUpdatesPeriodInDays"
                Comparison="GreaterThanOrEqualTo" Data="1" Key="HKEY_LOCAL_MACHINE"
                Subkey="Software\Microsoft\WindowsUpdate\UX\Settings"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
            </lar:And>
            <lar:And><!-- Applicable only if the WindowsVersion is between 10.0.0.10586 to
              10.0.18363.* -->
              <bar:WindowsVersion Comparison="GreaterThanOrEqualTo" BuildNumber="15063"
                MajorVersion="10" MinorVersion="0" ProductType="1"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:WindowsVersion Comparison="LessThanOrEqualTo" BuildNumber="18363"
                MajorVersion="10" MinorVersion="0" ProductType="1"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:RegDword Value="DeferQualityUpdatesPeriodInDays"
                Comparison="GreaterThanOrEqualTo" Data="1" Key="HKEY_LOCAL_MACHINE"
                Subkey="Software\Microsoft\WindowsUpdate\UX\Settings"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
            </lar:And>
            <lar:And><!-- Applicable only if the WindowsVersion is between 10.0.0.10586 to
              10.0.15062.* -->
              <bar:WindowsVersion Comparison="GreaterThanOrEqualTo" BuildNumber="10586"
                MajorVersion="10" MinorVersion="0" ProductType="1"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
              <bar:WindowsVersion Comparison="LessThanOrEqualTo" BuildNumber="15062"
                MajorVersion="10" MinorVersion="0" ProductType="1"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" /><!--
              Check for UX  -->
              <bar:RegDword Value="DeferUpgrade" Comparison="EqualTo" Data="1"
                Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\WindowsUpdate\UX\Settings"
                xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
            </lar:And>
            <lar:Or>
              <lar:And><!-- Applicable only if the WindowsVersion is greater than or equal to
                10.0.0.14393 -->
                <bar:WindowsVersion Comparison="GreaterThanOrEqualTo" BuildNumber="14393"
                  MajorVersion="10" MinorVersion="0" ProductType="1"
                  xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                <lar:Or><!-- Checking the Defer quality updates or defer feature updates is enabled
                  which indicates a WUFB device --><!--  DEFER FEATURE UPDATES  -->
                  <bar:RegValueExists Value="DeferFeatureUpdatesPeriodInDays"
                    Key="HKEY_LOCAL_MACHINE"
                    Subkey="Software\Policies\Microsoft\Windows\WindowsUpdate" Type="REG_DWORD"
                    xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                  <lar:And>
                    <bar:RegValueExists Value="DeferFeatureUpdatesPeriodInDays"
                      Key="HKEY_LOCAL_MACHINE"
                      Subkey="Software\Microsoft\PolicyManager\current\device\update"
                      Type="REG_DWORD"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <bar:RegDword Value="DeferFeatureUpdatesPeriodInDays_ProviderSet"
                      Comparison="EqualTo" Data="1" Key="HKEY_LOCAL_MACHINE"
                      Subkey="Software\Microsoft\PolicyManager\current\device\update"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <lar:Not>
                      <bar:RegDword Value="DeferFeatureUpdatesPeriodInDays_LastWrite"
                        Comparison="EqualTo" Data="1" Key="HKEY_LOCAL_MACHINE"
                        Subkey="Software\Microsoft\PolicyManager\current\device\update"
                        xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    </lar:Not>
                  </lar:And><!--
                  DEFER QUALITY UPDATES  -->
                  <bar:RegValueExists Value="DeferQualityUpdatesPeriodInDays"
                    Key="HKEY_LOCAL_MACHINE"
                    Subkey="Software\Policies\Microsoft\Windows\WindowsUpdate" Type="REG_DWORD"
                    xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                  <lar:And>
                    <bar:RegValueExists Value="DeferQualityUpdatesPeriodInDays"
                      Key="HKEY_LOCAL_MACHINE"
                      Subkey="Software\Microsoft\PolicyManager\current\device\update"
                      Type="REG_DWORD"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <bar:RegDword Value="DeferQualityUpdatesPeriodInDays_ProviderSet"
                      Comparison="EqualTo" Data="1" Key="HKEY_LOCAL_MACHINE"
                      Subkey="Software\Microsoft\PolicyManager\current\device\update"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <lar:Not>
                      <bar:RegDword Value="DeferQualityUpdatesPeriodInDays_LastWrite"
                        Comparison="EqualTo" Data="1" Key="HKEY_LOCAL_MACHINE"
                        Subkey="Software\Microsoft\PolicyManager\current\device\update"
                        xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    </lar:Not>
                  </lar:And>
                </lar:Or>
              </lar:And><!--
              This block checks if the target release version WUfB policy is configured -->
              <lar:And><!-- TargetReleaseVersion is applicable on RS4+, i.e. WindowsVersion greater
                than or equal to 10.0.0.17134. For simplicity we do not consider the UBR -->
                <bar:WindowsVersion Comparison="GreaterThanOrEqualTo" BuildNumber="17134"
                  MajorVersion="10" MinorVersion="0" ProductType="1"
                  xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                <lar:Or><!-- Check if TargetReleaseVersion policy is enabled which indicates a WUFB
                  device -->
                  <bar:RegValueExists Value="TargetReleaseVersionInfo" Key="HKEY_LOCAL_MACHINE"
                    Subkey="Software\Policies\Microsoft\Windows\WindowsUpdate" Type="REG_SZ"
                    xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                  <lar:And>
                    <bar:RegValueExists Value="TargetReleaseVersion" Key="HKEY_LOCAL_MACHINE"
                      Subkey="Software\Microsoft\PolicyManager\current\device\update" Type="REG_SZ"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <bar:RegDword Value="TargetReleaseVersion_ProviderSet" Comparison="EqualTo"
                      Data="1" Key="HKEY_LOCAL_MACHINE"
                      Subkey="Software\Microsoft\PolicyManager\current\device\update"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <lar:Not>
                      <bar:RegDword Value="TargetReleaseVersion_LastWrite" Comparison="EqualTo"
                        Data="1" Key="HKEY_LOCAL_MACHINE"
                        Subkey="Software\Microsoft\PolicyManager\current\device\update"
                        xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    </lar:Not>
                  </lar:And>
                </lar:Or>
              </lar:And><!--
              This block checks if the legacy upgrade deferral and update deferral WUfB policies are
              configured -->
              <lar:And><!-- Applicable only if the WindowsVersion is between 10.0.0.10586 to
                10.0.17134.* -->
                <bar:WindowsVersion Comparison="GreaterThanOrEqualTo" BuildNumber="10586"
                  MajorVersion="10" MinorVersion="0" ProductType="1"
                  xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                <bar:WindowsVersion Comparison="LessThanOrEqualTo" BuildNumber="17134"
                  MajorVersion="10" MinorVersion="0" ProductType="1"
                  xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                <lar:Or><!-- Defer Upgrade is a pre-req to using deferrals using legacy WUfB
                  policies --><!--  Check for GPO  -->
                  <bar:RegDword Value="DeferUpgrade" Comparison="EqualTo" Data="1"
                    Key="HKEY_LOCAL_MACHINE"
                    Subkey="SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"
                    xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" /><!--
                  Check for MDM  -->
                  <lar:And>
                    <bar:RegDword Value="RequireDeferUpgrade" Comparison="EqualTo" Data="1"
                      Key="HKEY_LOCAL_MACHINE"
                      Subkey="SOFTWARE\Microsoft\PolicyManager\current\device\Update"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" /><!--
                    On RS1 and above <Policy>_ProviderSet = 1 indicates that the policy was
                    explicitly configured -->
                    <lar:Or>
                      <lar:And>
                        <bar:WindowsVersion Comparison="GreaterThanOrEqualTo" BuildNumber="14393"
                          MajorVersion="10" MinorVersion="0" ProductType="1"
                          xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                        <bar:RegDword Value="RequireDeferUpgrade_ProviderSet" Comparison="EqualTo"
                          Data="1" Key="HKEY_LOCAL_MACHINE"
                          Subkey="Software\Microsoft\PolicyManager\current\device\update"
                          xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                      </lar:And>
                      <bar:WindowsVersion Comparison="LessThanOrEqualTo" BuildNumber="14392"
                        MajorVersion="10" MinorVersion="0" ProductType="1"
                        xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    </lar:Or>
                    <lar:Not>
                      <bar:RegDword Value="RequireDeferUpgrade_LastWrite" Comparison="EqualTo"
                        Data="1" Key="HKEY_LOCAL_MACHINE"
                        Subkey="SOFTWARE\Microsoft\PolicyManager\current\device\Update"
                        xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    </lar:Not>
                  </lar:And>
                </lar:Or>
                <lar:Or><!-- Check for Defer upgrade period and it's valid values are from 0 - 8
                  months --><!--  Check for GPO  -->
                  <lar:And>
                    <bar:RegValueExists Value="DeferUpgradePeriod" Key="HKEY_LOCAL_MACHINE"
                      Subkey="SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" Type="REG_DWORD"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <bar:RegDword Value="DeferUpgradePeriod" Comparison="LessThanOrEqualTo" Data="8"
                      Key="HKEY_LOCAL_MACHINE"
                      Subkey="SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                  </lar:And><!--
                  Check for Defer upgrade period and it's valid values are from 0 - 8 months --><!--
                  Check for GPO  -->
                  <lar:And>
                    <bar:RegValueExists Value="DeferUpdatePeriod" Key="HKEY_LOCAL_MACHINE"
                      Subkey="SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" Type="REG_DWORD"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <bar:RegDword Value="DeferUpdatePeriod" Comparison="LessThanOrEqualTo" Data="4"
                      Key="HKEY_LOCAL_MACHINE"
                      Subkey="SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                  </lar:And><!--
                  Check for Defer upgrade period in MDM  --><!--
                  On RS1 and above <Policy>_ProviderSet = 1 indicates that the policy was explicitly
                  configured -->
                  <lar:And>
                    <bar:RegValueExists Value="DeferUpgradePeriod" Key="HKEY_LOCAL_MACHINE"
                      Subkey="SOFTWARE\Microsoft\PolicyManager\current\device\Update"
                      Type="REG_DWORD"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <bar:RegDword Value="DeferUpgradePeriod" Comparison="LessThanOrEqualTo" Data="8"
                      Key="HKEY_LOCAL_MACHINE"
                      Subkey="SOFTWARE\Microsoft\PolicyManager\current\device\Update"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <lar:Or>
                      <lar:And>
                        <bar:WindowsVersion Comparison="GreaterThanOrEqualTo" BuildNumber="14393"
                          MajorVersion="10" MinorVersion="0" ProductType="1"
                          xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                        <bar:RegDword Value="DeferUpgradePeriod_ProviderSet" Comparison="EqualTo"
                          Data="1" Key="HKEY_LOCAL_MACHINE"
                          Subkey="Software\Microsoft\PolicyManager\current\device\update"
                          xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                      </lar:And>
                      <bar:WindowsVersion Comparison="LessThanOrEqualTo" BuildNumber="14392"
                        MajorVersion="10" MinorVersion="0" ProductType="1"
                        xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    </lar:Or>
                    <lar:Not>
                      <bar:RegDword Value="DeferUpgradePeriod_LastWrite" Comparison="EqualTo"
                        Data="1" Key="HKEY_LOCAL_MACHINE"
                        Subkey="SOFTWARE\Microsoft\PolicyManager\current\device\Update"
                        xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    </lar:Not>
                  </lar:And><!--
                  Check for Defer update period in MDM  --><!--
                  On RS1 and above <Policy>_ProviderSet = 1 indicates that the policy was explicitly
                  configured -->
                  <lar:And>
                    <bar:RegValueExists Value="DeferUpdatePeriod" Key="HKEY_LOCAL_MACHINE"
                      Subkey="SOFTWARE\Microsoft\PolicyManager\current\device\Update"
                      Type="REG_DWORD"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <bar:RegDword Value="DeferUpdatePeriod" Comparison="LessThanOrEqualTo" Data="4"
                      Key="HKEY_LOCAL_MACHINE"
                      Subkey="SOFTWARE\Microsoft\PolicyManager\current\device\Update"
                      xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    <lar:Or>
                      <lar:And>
                        <bar:WindowsVersion Comparison="GreaterThanOrEqualTo" BuildNumber="14393"
                          MajorVersion="10" MinorVersion="0" ProductType="1"
                          xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                        <bar:RegDword Value="DeferUpdatePeriod_ProviderSet" Comparison="EqualTo"
                          Data="1" Key="HKEY_LOCAL_MACHINE"
                          Subkey="Software\Microsoft\PolicyManager\current\device\update"
                          xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                      </lar:And>
                      <bar:WindowsVersion Comparison="LessThanOrEqualTo" BuildNumber="14392"
                        MajorVersion="10" MinorVersion="0" ProductType="1"
                        xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    </lar:Or>
                    <lar:Not>
                      <bar:RegDword Value="DeferUpdatePeriod_LastWrite" Comparison="EqualTo"
                        Data="1" Key="HKEY_LOCAL_MACHINE"
                        Subkey="SOFTWARE\Microsoft\PolicyManager\current\device\Update"
                        xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" />
                    </lar:Not>
                  </lar:And>
                </lar:Or>
              </lar:And>
            </lar:Or>
          </lar:Or>
        </lar:And>
      </lar:Not>
    </upd:IsInstalled>
  </upd:ApplicabilityRules>
</upd:Update>
