[package]
name = "data_collection"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
database = { path = "../database" }
api = { path = "../api" }
utils = { path = "../utils" }
shell = { path = "../shell" }
anyhow = { version = "1.0.94", features = ["backtrace"] }
serde_json = "1.0.134"
thiserror = "2.0.4"
tokio = { version = "1.43.0", features = ["full", "tracing"] }
serde = "1.0.219"
sysinfo = "0.33.1"
regex = "1.11.1"
netstat2 = "0.11.1"
netdev = "0.32.0"
ordered-float = { version = "4.6.0", features = ["serde"] }
rustls-native-certs = "0.8.1"
x509-parser = "0.17.0"
chrono = "0.4.39"
glob = "0.3.2"
rayon = "1.10.0"
battery = "0.7.8"
async-trait = "0.1.87"
cfg-if = "1.0.0"

[target.'cfg(unix)'.dependencies]
libc = "0.2.170"

[target.'cfg(target_os = "macos")'.dependencies]
sysctl = "0.6.0"
core-foundation = "0.10.0"
plist = "1.7.0"
io-kit-sys = "0.4.1"
system-configuration = "0.6.1"

[target.'cfg(target_os = "linux")'.dependencies]
csv = "1.3.1"
smbios-lib = "0.9.2"
zbus = "5.5.0"

[target.'cfg(windows)'.dependencies]
windows-acl = "0.3.0"
windows = { version = "0.59.0", features = [
  "Win32_Security",
  "Win32_Security_Authorization",
  "Win32_NetworkManagement_NetManagement",
  "Win32_System_SystemInformation",
  "Win32_System_RemoteDesktop",
  "Win32_System_Services",
] }
windows_patch_xml_checker = { path = "../windows_patch_xml_checker" }
windows_registry = { path = "../windows_registry" }
quick-xml = { version = "0.38.0", features = ["serialize"] }
schannel = "0.1.27"
rustls-pki-types = "1.11.0"
