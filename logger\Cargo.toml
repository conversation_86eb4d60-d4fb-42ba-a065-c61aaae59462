[package]
name = "logger"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
utils = { path = "../utils" }
anyhow = { version = "1.0.94", features = ["backtrace"] }
chrono = "0.4.38"
thiserror = "2.0.4"
cfg-if = "1.0.0"
time = { version = "0.3.37", features = [
  "local-offset",
  "formatting",
  "parsing",
] }
tracing = { version = "0.1.41", features = ["log", "std"] }
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.19", features = [
  "local-time",
  "env-filter",
  "chrono",
] }
# console-subscriber = "0.4.1"
