use crate::{has_commands::HasCommands, tasks::command_executor::CommandExecutor, TaskExecutable};
use anyhow::{anyhow, Error};
use database::models::FileAttachment;
use logger::{debug, error};
use shell::ShellOutput;

pub struct Rpm<'a> {
    commands: Box<dyn HasCommands>,
    attachment: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Rpm<'a> {
    pub fn new(
        commands: Box<dyn HasCommands>,
        attachment: &'a FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            commands,
            attachment,
            task,
        }
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        let command = self.commands.get_install_command()
            .map(|i| i.to_owned())
            .unwrap_or(match os_info::get().os_type() {
              os_info::Type::CentOS | os_info::Type::RedHatEnterprise | os_info::Type::Redhat => format!("yum -y --disablerepo=* --setopt=protected_multilib=false --disableplugin=subscription-manager,search-disabled-repos install {}", self.attachment.real_name),
              os_info::Type::openSUSE => format!("zypper --no-refresh -n in {}", self.attachment.real_name),
              _ => "".to_owned()
          });

        Ok(CommandExecutor::new_command(&command, self.task)
            .execute()
            .await?)
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        let pkg_finder_command = r#"rpm -q --qf "%{NAME}\n\" -p __PACKAGE__"#
            .replace("__PACKAGE__", &self.attachment.real_name);
        let pkg_finder_result =
            CommandExecutor::new_command(&pkg_finder_command, self.task.clone())
                .capture()
                .execute()
                .await;

        if let Err(error) = pkg_finder_result {
            error!(?error, "Failed to find package id for deb package");
            Err(anyhow!("Failed to find package id for deb package"))
        } else {
            let package_id = pkg_finder_result.unwrap().output;

            debug!("Got package id output: {}", package_id);

            let command = self
                .commands
                .get_uninstall_command()
                .map(|i| i.to_owned())
                .unwrap_or(format!("rpm -ev {}", package_id.trim()));

            Ok(CommandExecutor::new_command(&command, self.task)
                .execute()
                .await?)
        }
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        let command = self.commands.get_upgrade_command()
          .map(|i| i.to_owned())
          .unwrap_or(match os_info::get().os_type() {
            os_info::Type::CentOS | os_info::Type::RedHatEnterprise | os_info::Type::Redhat => format!("yum -y --disablerepo=* --setopt=protected_multilib=false --disableplugin=subscription-manager,search-disabled-repos install {}", self.attachment.real_name),
            os_info::Type::openSUSE => format!("zypper --no-refresh -n in {}", self.attachment.real_name),
            _ => "".to_owned()
        });

        Ok(CommandExecutor::new_command(&command, self.task)
            .execute()
            .await?)
    }
}
