use crate::WMIGetter;
use logger::{debug, error};
use serde::Deserialize;

#[derive(Deserialize, Debug)]
#[serde(rename = "Win32_OperatingSystem")]
#[serde(rename_all = "PascalCase")]
struct SystemScannedValue {
    pub mui_languages: Vec<String>,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct WindowsLanguage {
    #[serde(rename = "@Language")]
    language: Option<String>,
}

impl WindowsLanguage {
    pub fn evaluate(&self) -> bool {
        debug!("Inspecting Windows Language");
        match WMIGetter::new(None) {
            Ok(wmi_connection) => match wmi_connection.get::<SystemScannedValue>() {
                Some(result) => result
                    .mui_languages
                    .contains(self.language.as_ref().unwrap()),
                None => false,
            },
            Err(error) => {
                error!(?error, "Failed to open wmi connection to read system value");
                false
            }
        }
    }
}
