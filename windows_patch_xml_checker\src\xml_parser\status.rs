use std::fmt::{self, Display};

use serde::Deserialize;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Deserialize, Default)]
pub enum PatchStatus {
    Missing,
    Installed,
    FileMissing,
    Ready,
    NotReady,
    PreRequisiteFileMissing,
    PartiallyInstalled,
    InvalidFile,
    #[default]
    Unknown,
}

impl Display for PatchStatus {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            PatchStatus::Missing => write!(f, "Missing"),
            PatchStatus::Installed => write!(f, "Installed"),
            PatchStatus::FileMissing => write!(f, "FileMissing"),
            PatchStatus::Ready => write!(f, "Ready"),
            PatchStatus::NotReady => write!(f, "NotReady"),
            PatchStatus::PreRequisiteFileMissing => write!(f, "PreRequisiteFileMissing"),
            PatchStatus::PartiallyInstalled => write!(f, "PartiallyInstalled"),
            PatchStatus::InvalidFile => write!(f, "InvalidFile"),
            PatchStatus::Unknown => write!(f, "Unknown"),
        }
    }
}

impl From<&str> for PatchStatus {
    fn from(value: &str) -> Self {
        match value.to_lowercase().as_str() {
            "missing" => PatchStatus::Missing,
            "installed" => PatchStatus::Installed,
            "filemissing" => PatchStatus::FileMissing,
            "ready" => PatchStatus::Ready,
            "notready" => PatchStatus::NotReady,
            "prerequisitefilemissing" => PatchStatus::PreRequisiteFileMissing,
            "partiallyinstalled" => PatchStatus::PartiallyInstalled,
            "invalidfile" => PatchStatus::InvalidFile,
            _ => PatchStatus::Unknown,
        }
    }
}

impl From<u8> for PatchStatus {
    fn from(value: u8) -> Self {
        match value {
            1 => PatchStatus::Missing,
            2 => PatchStatus::Installed,
            3 => PatchStatus::FileMissing,
            4 => PatchStatus::Ready,
            5 => PatchStatus::NotReady,
            6 => PatchStatus::PreRequisiteFileMissing,
            7 => PatchStatus::PartiallyInstalled,
            8 => PatchStatus::InvalidFile,
            _ => PatchStatus::Unknown,
        }
    }
}

impl Into<u8> for PatchStatus {
    fn into(self) -> u8 {
        match self {
            PatchStatus::Missing => 1,
            PatchStatus::Installed => 2,
            PatchStatus::FileMissing => 3,
            PatchStatus::Ready => 4,
            PatchStatus::NotReady => 5,
            PatchStatus::PreRequisiteFileMissing => 6,
            PatchStatus::PartiallyInstalled => 7,
            PatchStatus::InvalidFile => 8,
            PatchStatus::Unknown => 9,
        }
    }
}

impl PatchStatus {
    pub fn is_applicable(&self) -> bool {
        self == &PatchStatus::Installed
            || self == &PatchStatus::Ready
            || self == &PatchStatus::Missing
    }

    pub fn is_missing(&self) -> bool {
        self == &PatchStatus::Missing || self == &PatchStatus::Ready
    }

    pub fn is_installed(&self) -> bool {
        self == &PatchStatus::Installed
    }
}
