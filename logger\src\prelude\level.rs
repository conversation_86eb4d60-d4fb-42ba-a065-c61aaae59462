use core::fmt;

use tracing::Level as TracingLevel;

#[derive(Debug, Default, Clone)]
pub enum Level {
    TRACE,
    DEBUG,
    #[default]
    INFO,
    WARN,
    ERROR,
}

impl<'a> Into<&'a str> for Level {
    fn into(self) -> &'a str {
        match self {
            Level::TRACE => "trace",
            Level::DEBUG => "debug",
            Level::INFO => "info",
            Level::WARN => "warn",
            Level::ERROR => "error",
        }
    }
}

impl Into<String> for Level {
    fn into(self) -> String {
        match self {
            Level::TRACE => "trace",
            Level::DEBUG => "debug",
            Level::INFO => "info",
            Level::WARN => "warn",
            Level::ERROR => "error",
        }
        .to_owned()
    }
}

impl From<&str> for Level {
    fn from(value: &str) -> Self {
        match value {
            "trace" => Level::TRACE,
            "debug" => Level::DEBUG,
            "info" => Level::INFO,
            "warn" => Level::WARN,
            "error" => Level::ERROR,
            _ => Level::ERROR,
        }
    }
}

impl From<String> for Level {
    fn from(value: String) -> Self {
        match value.as_str() {
            "trace" => Level::TRACE,
            "debug" => Level::DEBUG,
            "info" => Level::INFO,
            "warn" => Level::WARN,
            "error" => Level::ERROR,
            _ => Level::ERROR,
        }
    }
}

impl Into<TracingLevel> for Level {
    fn into(self) -> TracingLevel {
        match self {
            Level::TRACE => TracingLevel::TRACE,
            Level::DEBUG => TracingLevel::DEBUG,
            Level::INFO => TracingLevel::INFO,
            Level::WARN => TracingLevel::WARN,
            Level::ERROR => TracingLevel::ERROR,
        }
    }
}

impl Into<Level> for TracingLevel {
    fn into(self) -> Level {
        match self {
            TracingLevel::TRACE => Level::TRACE,
            TracingLevel::DEBUG => Level::DEBUG,
            TracingLevel::INFO => Level::INFO,
            TracingLevel::WARN => Level::WARN,
            TracingLevel::ERROR => Level::ERROR,
        }
    }
}

impl fmt::Display for Level {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(
            f,
            "{}",
            match self {
                Level::TRACE => "trace",
                Level::DEBUG => "debug",
                Level::INFO => "info",
                Level::WARN => "warn",
                Level::ERROR => "error",
            }
        )
    }
}
