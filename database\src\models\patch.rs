use super::FileAttachment;
use crate::<PERSON><PERSON><PERSON>;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Patch {
    pub id: PrimaryKey,
    pub name: Option<String>,
    #[serde(alias = "title")]
    pub display_name: Option<String>,
    pub description: Option<String>,
    pub patch_update_category: Option<String>,
    pub is_third_party: Option<bool>,
    #[serde(alias = "downloadFileDetails")]
    pub download_files: Vec<FileAttachment>,
    pub product_type: Option<String>,
    #[serde(alias = "atLeastOneFileInstallation")]
    pub should_succeed_on_single_file_installation: bool,
}
