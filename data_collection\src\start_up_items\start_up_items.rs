use super::start_up_item::StartUpItem;
use crate::{DataCollectionError, DataCollectionExtension};
use anyhow::Result;
use async_trait::async_trait;
use database::models::AgentMetadata;
use logger::{debug, error};
use serde_json::{json, Value};
use std::{collections::HashSet, time::Instant};

#[derive(Debug)]
pub struct StartUpItems<'a> {
    items: HashSet<StartUpItem>,
    agent_metadata: &'a AgentMetadata,
}

impl<'a> StartUpItems<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> Self {
        Self {
            agent_metadata,
            items: HashSet::new(),
        }
    }
}

#[async_trait]
impl<'a> DataCollectionExtension for StartUpItems<'a> {
    fn get_refresh_interval(&self) -> u64 {
        self.agent_metadata
            .get_agent_refresh_settings()
            .startup_items_refresh_cycle
    }

    fn get_name(&self) -> &str {
        "start_up_items_ext"
    }

    fn get_endpoint_id(&self) -> i64 {
        self.agent_metadata.get_endpoint_id()
    }

    fn build_payload(&self) -> Result<Value, DataCollectionError> {
        Ok(json!({
            "asset_id" : self.get_endpoint_id(),
            "data" : json!({
                "startup_item_details": self.items,
            })
        }))
    }

    async fn collect(&mut self) -> Result<(), DataCollectionError> {
        let logger = self.logger();
        match tokio::task::Builder::new()
            .name(self.get_name())
            .spawn_blocking(move || {
                logger.with(|| {
                    let time = Instant::now();
                    let collection = StartUpItem::collect();
                    debug!("Time taken for collection {:?}", time.elapsed());
                    collection
                })
            })
            .unwrap()
            .await
        {
            Ok(data) => {
                self.items = data;
                Ok(())
            }
            Err(error) => {
                error!(?error, "Failed to join task for {}", self.get_name());
                Err(error.into())
            }
        }
    }
}
