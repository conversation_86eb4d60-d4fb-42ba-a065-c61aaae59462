pub static SHUTDOWN_TIME: usize = 5;
pub static MAX_FILE_SIZE_FOR_HASHING: u64 = 1024 * 1024 * 100; // 1GB

// driver constants
#[cfg(windows)]
pub static DRIVER_FILE_NAME: &str = "ZirozenDrv.inf";
#[cfg(windows)]
pub static DRIVER_SYS_NAME: &str = "ZirozenDrv.sys";
#[cfg(windows)]
pub static DRIVER_NAME: &str = "ZirozenDrv";
#[cfg(windows)]
pub static DRIVER_SERVICE_NAME: &str = "ZirozenDrv";

// manager service constants
#[cfg(windows)]
pub static MANAGER_SERVICE_NAME: &str = "EndpointOps Manager";
#[cfg(not(windows))]
pub static MANAGER_SERVICE_NAME: &str = "com.zirozen.manager";
#[cfg(windows)]
pub static MANAGER_BINARY_NAME: &str = "endpointops.exe";
#[cfg(not(windows))]
pub static MANAGER_BINARY_NAME: &str = "endpointops";

// endpointops service constants
#[cfg(windows)]
pub static ENDPOINTOPS_SERVICE_NAME: &str = "EndpointOps";
#[cfg(not(windows))]
pub static ENDPOINTOPS_SERVICE_NAME: &str = "com.zirozen.endpointops";
#[cfg(windows)]
pub static ENDPOINTOPS_BINARY_NAME: &str = "endpointops.exe";
#[cfg(not(windows))]
pub static ENDPOINTOPS_BINARY_NAME: &str = "endpointops";

// RDP meshagent constants
#[cfg(target_os = "linux")]
pub static MESH_AGENT_SERVICE_NAME: &str = "zirozen\\x2dremote\\x2ddesktop";
#[cfg(not(target_os = "linux"))]
pub static MESH_AGENT_SERVICE_NAME: &str = "zirozen-remote-desktop";
#[cfg(windows)]
pub static MESH_AGENT_BINARY_NAME: &str = "zirozen-remote-desktop-agent64-zirozen.exe";
#[cfg(not(windows))]
pub static MESH_AGENT_BINARY_NAME: &str = "zirozen-remote-desktop-agent";
