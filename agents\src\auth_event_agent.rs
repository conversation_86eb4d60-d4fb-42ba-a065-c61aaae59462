use agent_manager::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use anyhow::Result;
use api::data_collection::send_fim_data;
use async_trait::async_trait;
use auth_event::{get_default_monitor, AuthEvent};
use database::{models::AgentMetadata, Model};
use logger::{debug, error, info};
use serde_json::json;
use std::sync::Arc;
use tokio::select;
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::sync::Mutex;
use tokio::task::Join<PERSON>andle;

pub struct AuthEventAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    stop_signal_sender: Sender<bool>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl AuthEventAgent<'static> {
    pub fn new(agent_metadata: &'static AgentMetadata) -> AuthEventAgent<'static> {
        let (stop_signal_sender, stop_signal_receiver) = channel(1);

        AuthEventAgent {
            agent_metadata,
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            stop_signal_sender,
        }
    }

    fn spawn_sync_task(
        agent_metadata: &AgentMetadata,
        mut rx: Receiver<AuthEvent>,
    ) -> JoinHandle<()> {
        let endpoint_id = agent_metadata.get_endpoint_id();
        tokio::task::spawn(async move {
            while let Some(event) = rx.recv().await {
                if let Err(error) = send_fim_data(json!({
                        "asset_id" : endpoint_id,
                        "data" : json!({
                            "auth_events": [event],
                        })
                }))
                .await
                {
                    if let Err(error) = event.persist().await {
                        error!(?error, "Failed to unsend auth event to database");
                    };
                    error!(?error, "Failed to sync login event");
                } else {
                    debug!("Sent Event {:?} to server", event);
                }
            }
        })
    }

    fn spawn_monitor_task(rx: Receiver<()>, tx: Sender<AuthEvent>) -> JoinHandle<()> {
        tokio::task::spawn(async {
            match get_default_monitor().monitor(rx, tx).await {
                Ok(_) => {
                    debug!("Finished running monitor");
                }
                Err(error) => {
                    error!(?error, "Failed to monitor auth events");
                }
            };
        })
    }
}

#[async_trait]
impl AgentRunnable for AuthEventAgent<'static> {
    fn get_name(&self) -> &str {
        "auth_events_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting Auth Events Agent ------------------------");

        let mut shutdown_receiver = self.stop_signal_receiver.lock().await;

        let (tx, rx) = channel(10);

        let (shutdown_tx, shutdown_rx) = channel(1);

        let mut sync_task = AuthEventAgent::spawn_sync_task(self.agent_metadata, rx);

        let mut monitor_task = AuthEventAgent::spawn_monitor_task(shutdown_rx, tx);

        loop {
            select! {
                biased;

                _ = shutdown_receiver.recv() => {
                    info!("Shutting Down Auth Events Agent");
                    if let Err(error) = shutdown_tx.send(()).await {
                        error!(?error, "Failed to send shutdown signal to monitor task");
                    }
                    break;
                },

                _ = &mut sync_task => {

                    info!("Event Sync task is finished executing");
                }

                _ = &mut monitor_task => {
                    info!("Event Monitor task is finished executing");
                }

            }
        }
        info!("---------------------- Stopped Auth Events Agent ------------------------");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if let Err(error) = self.stop_signal_sender.send(true).await {
            error!(?error, "Failed to send stop signal to Auth Events agent");
        }
        Ok(())
    }

    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
}
