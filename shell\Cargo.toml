[package]
name = "shell"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
utils = { path = "../utils" }
thiserror = "2.0.4"
anyhow = { version = "1.0.94", features = ["backtrace"] }
subprocess = "0.2.9"
cfg-if = "1.0.0"

[target.'cfg(unix)'.dependencies]
shell-words = "1.1.0"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.59.0", features = ["Win32_UI_Shell"] }
