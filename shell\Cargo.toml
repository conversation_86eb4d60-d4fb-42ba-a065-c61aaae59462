[package]
name = "shell"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
utils = { path = "../utils" }
thiserror = "2.0.4"
anyhow = { version = "1.0.94", features = ["backtrace"] }
tokio = { version = "1.43.0", features = ["full", "tracing"] }
cfg-if = "1.0.0"
tokio-stream = { version = "0.1.17", features = ["io-util"] }

[target.'cfg(unix)'.dependencies]
shell-words = "1.1.0"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.59.0", features = ["Win32_UI_Shell"] }
