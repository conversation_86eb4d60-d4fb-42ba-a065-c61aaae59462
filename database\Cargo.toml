[package]
name = "database"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
utils = { path = "../utils" }
anyhow = { version = "1.0.94", features = ["backtrace"] }
serde = { version = "1.0.219", features = ["alloc", "derive", "rc"] }
serde_json = "1.0.133"
surrealdb = { version = "2.2.1", features = ["kv-rocksdb"] }
thiserror = "2.0.3"
tokio = { version = "1.43.0", features = ["full", "tracing"] }
serde_with = "3.12.0"
chrono = "0.4.39"
sysinfo = "0.33.1"
futures = { version = "0.3.31", features = ["executor"] }
async-trait = "0.1.87"
hex = "0.4.3"
sha1 = "0.10.6"
sha2 = "0.10.8"
md-5 = "0.10.6"
