use crate::{args::CmdArgs, install::install_and_start_service, prelude::EndpointopsError};
use anyhow::anyhow;
use api::agent::is_endpointops_update_available;
use database::models::ServerConfig;
use logger::{debug, error, info, ModuleLogger, WithSubscriber};
use std::path::PathBuf;
use utils::{
    constants::{ENDPOINTOPS_BINARY_NAME, ENDPOINTOPS_SERVICE_NAME},
    dir::get_current_dir,
    service_manager::{ServiceManager, ServiceStatus},
};

use super::run_upgrade_task;
use std::env;
pub struct EndpointOpsManager {
    endpointops_service: ServiceManager,
}

impl EndpointOpsManager {
    pub fn new(args: &CmdArgs) -> Self {
        Self {
            endpointops_service: ServiceManager::new(
                ENDPOINTOPS_SERVICE_NAME.to_owned(),
                PathBuf::from(&args.install_path).join(ENDPOINTOPS_BINARY_NAME),
                Some(PathBuf::from(&args.install_path)),
                vec![],
            ),
        }
    }

    pub async fn check_for_upgrade(
        &mut self,
        server_config: &ServerConfig,
    ) -> Result<(), EndpointopsError> {
        debug!("Checking if agent upgrade is available");
        match is_endpointops_update_available(server_config.uuid()).await {
            Ok(is_upgrade_available) => {
                if is_upgrade_available {
                    // first stop endpointops
                    self.stop_endpointops()?;

                    let exe_path = env::current_exe().unwrap();

                    info!("EXE path {}", exe_path.display());

                    match EndpointOpsManager::upgrade_endpointops().await {
                        Ok(()) => {
                            info!("Agent has been upgraded successfully!");
                            info!("Restarting Agent...");
                            #[cfg(windows)]
                            {
                                use std::os::windows::process::CommandExt;
                                use std::process::Command;
                                use utils::constants::MANAGER_SERVICE_NAME;

                                if let Err(error) = Command::new("cmd.exe")
                                    .arg("/C")
                                    .raw_arg(format!(
                                        "timeout /t 3 /nobreak >nul && sc.exe start \"{}\"",
                                        MANAGER_SERVICE_NAME
                                    ))
                                    .spawn()
                                {
                                    error!(?error, "Failed to spawn restart service command");
                                };
                                std::process::exit(0)
                            }
                            #[cfg(not(windows))]
                            {
                                match cargo_util::ProcessBuilder::new(exe_path)
                                    .args(&env::args_os().skip(1).collect::<Vec<_>>())
                                    .exec_replace()
                                {
                                    Ok(_) => std::process::exit(0),
                                    Err(error) => {
                                        error!(?error, "Failed to build process from cargo util");
                                        std::process::exit(
                                            error
                                                .downcast::<cargo_util::ProcessError>()
                                                .unwrap()
                                                .code
                                                .unwrap_or(1),
                                        );
                                    }
                                }
                            }
                        }
                        Err(error) => {
                            error!(?error, "Failed to upgrade agent to latest version");
                            Err(error)
                        }
                    }
                } else {
                    debug!("No Update available");
                    Ok(())
                }
            }
            Err(error) => {
                error!(?error, "Failed to check for update");
                Err(error.into())
            }
        }
    }

    async fn upgrade_endpointops() -> Result<(), EndpointopsError> {
        let module_loger = ModuleLogger::new("global", None, Some("upgrade".to_owned()));

        let _guard = module_loger.guard();

        match run_upgrade_task()
            .with_subscriber(module_loger.subscriber())
            .await
        {
            Ok(exit_code) => {
                info!("Upgrade has been finished with exit code {}", exit_code);
                Ok(())
            }
            Err(error) => {
                error!(?error, "Failed to handle upgrade");
                Err(error)
            }
        }
    }

    pub fn start_endpointops(&mut self) -> Result<(), EndpointopsError> {
        match self.endpointops_service.status() {
            Ok(status) => {
                info!(
                    "Service {} status is {:?}",
                    ENDPOINTOPS_SERVICE_NAME, status
                );
                if status == ServiceStatus::NotInstalled {
                    info!(
                        "Service {} is not installed so installing it",
                        ENDPOINTOPS_SERVICE_NAME
                    );
                    install_and_start_service(
                        ENDPOINTOPS_SERVICE_NAME.to_owned(),
                        get_current_dir().join(ENDPOINTOPS_BINARY_NAME),
                        vec!["--agent".to_string()],
                    )?
                } else if status == ServiceStatus::Running {
                    info!("Service {} is already running", ENDPOINTOPS_SERVICE_NAME);
                } else {
                    match self.endpointops_service.start() {
                        Ok(()) => {
                            info!(
                                "Service {} has been started successfully",
                                ENDPOINTOPS_SERVICE_NAME
                            );
                        }
                        Err(error) => {
                            return Err(anyhow!(
                                "Failed to start service {} with error {:?}",
                                ENDPOINTOPS_SERVICE_NAME,
                                error
                            )
                            .into());
                        }
                    }
                }
            }
            Err(error) => {
                return Err(anyhow!(
                    "Failed to determine current status of service {} with error {:?}",
                    ENDPOINTOPS_SERVICE_NAME,
                    error
                )
                .into());
            }
        }
        Ok(())
    }

    pub fn stop_endpointops(&mut self) -> Result<(), EndpointopsError> {
        match self.endpointops_service.status() {
            Ok(status) => {
                if status == ServiceStatus::NotInstalled {
                    return Err(anyhow!(
                        "Service {} is not installed please reinstall agent",
                        ENDPOINTOPS_SERVICE_NAME
                    )
                    .into());
                }
                if status != ServiceStatus::Running {
                    info!(
                        "Service {} is not running so skipping stop operation",
                        ENDPOINTOPS_SERVICE_NAME
                    );
                } else {
                    match self.endpointops_service.stop() {
                        Ok(()) => {
                            info!(
                                "Service {} has been stopped successfully",
                                ENDPOINTOPS_SERVICE_NAME
                            );
                        }
                        Err(error) => {
                            return Err(anyhow!(
                                "Failed to stop service {} with error {:?}",
                                ENDPOINTOPS_SERVICE_NAME,
                                error
                            )
                            .into());
                        }
                    }
                }
            }
            Err(error) => {
                return Err(anyhow!(
                    "Failed to determine current status of service {} with error {:?}",
                    ENDPOINTOPS_SERVICE_NAME,
                    error
                )
                .into());
            }
        }
        Ok(())
    }
}
