use super::software::{PackageType, Software};
use crate::execute_in_thread_pool;
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde_json::json;
use std::option::Option;
use std::{
    collections::{HashMap, HashSet},
    path::PathBuf,
};
use utils::shutdown::is_system_running;
use win_registry::WinRegistry;

#[derive(Debug, Hash, PartialEq, Eq, Default)]
pub struct WindowsProgram {
    name: String,
    version: String,
    publisher: String,
    uninstall_string: String,
    install_date: String,
    install_location: String,
    install_source: String,
}

impl From<WindowsProgram> for Software {
    fn from(value: WindowsProgram) -> Self {
        Self {
            name: value.name,
            version: value.version,
            r#type: PackageType::WindowsPrograms,
            vendor: value.publisher,
            properties: json!({
                "uninstall_string": value.uninstall_string,
                "install_date": value.install_date,
                "install_location": value.install_location,
                "install_source": value.install_source
            }),
            ..Default::default()
        }
    }
}

impl WindowsProgram {
    fn generate_package(mut value: HashMap<String, String>) -> Option<WindowsProgram> {
        let mut program = WindowsProgram::default();
        if !(value.contains_key("DisplayName") && value.contains_key("DisplayVersion")) {
            return None;
        }
        if let Some(name) = value.remove("DisplayName") {
            program.name = name;
        }
        if let Some(version) = value.remove("DisplayVersion") {
            program.version = version;
        }
        if let Some(install_location) = value.remove("InstallLocation") {
            program.install_location = install_location;
        }
        if let Some(install_source) = value.remove("InstallSource") {
            program.install_source = install_source;
        }
        if let Some(publisher) = value.remove("Publisher") {
            program.publisher = publisher;
        }
        if let Some(install_date) = value.remove("InstallDate") {
            program.install_date = install_date;
        }
        if let Some(uninstall_string) = value.remove("UninstallString") {
            program.uninstall_string = uninstall_string;
        }

        Some(program)
    }

    fn collect_from_path(path: &str) -> HashSet<WindowsProgram> {
        let path_buf = PathBuf::from(path);
        let registry = match WinRegistry::new(&path_buf) {
            Ok(reg) => reg,
            Err(_) => return HashSet::new(),
        };

        registry
            .keys()
            .into_iter()
            .map(|key| WinRegistry::new(path_buf.join(key)))
            .filter_map(Result::ok)
            .map(|reg| WindowsProgram::generate_package(reg.values(None)))
            .filter(|i| i.is_some())
            .map(|i| i.unwrap())
            .collect()
    }

    pub fn collect() -> HashSet<Software> {
        let reg_paths = [
            "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall".to_owned(),
            "HKLM\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall".to_owned(),
        ]
        .into_iter()
        .chain(WinRegistry::get_users_key().into_iter().map(|key| {
            format!(
                "HKU\\{}\\Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
                key
            )
        }))
        .collect::<Vec<String>>();

        execute_in_thread_pool(|| {
            reg_paths
                .into_par_iter()
                .take_any_while(|_| is_system_running())
                .flat_map(|path| WindowsProgram::collect_from_path(&path))
                .map(|i| i.into())
                .collect()
        })
    }
}
