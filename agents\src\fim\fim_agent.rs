use crate::fim::fim_data_sync_agent::FIMDataSyncAgent;
use crate::fim::fim_watch_agent::FIMWatchAgent;
use agent_manager::{<PERSON>, AgentManager, AgentRunnable};
use anyhow::Result;
use async_trait::async_trait;
use database::models::{AgentMetadata, FIMConfig, FIMConfigType};
use database::{Model, PrimaryKey, Uuid};
use logger::{debug, error, info, WithSubscriber};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use sysinfo::{Disk, Disks};
use tokio::select;
use tokio::sync::broadcast::Sender;
use tokio::task::JoinHandle;
use tokio::time::sleep;
use utils::shutdown::get_shutdown_signal;

pub struct FIMAgent<'a> {
    agent_metadata: &'a AgentMetadata,
    restart_watch_agent_tx: Arc<Sender<bool>>,
}

impl FIMAgent<'static> {
    pub fn new(
        agent_metadata: &'static AgentMetadata,
        restart_watch_tx: Sender<bool>,
    ) -> FIMAgent<'static> {
        FIMAgent {
            agent_metadata,
            restart_watch_agent_tx: Arc::new(restart_watch_tx),
        }
    }

    async fn sholud_restart_watcher_for_usb(disks: &Disks) -> bool {
        let removeable_disk = disks
            .iter()
            .filter(|item| item.is_removable())
            .map(|item| (item.mount_point().to_string_lossy().to_string(), item))
            .collect::<HashMap<String, &Disk>>();
        // .collect::<Vec<&Disk>>();

        let fim_configs = match FIMConfig::default().get_all(None).await {
            Ok(configs) => configs,
            Err(error) => {
                error!(?error, "Failed to get all fim config for usb check");
                vec![]
            }
        }
        .into_iter()
        .filter(|config| config.is_usb.is_some_and(|v| v.to_owned()))
        .map(|config| {
            (
                config
                    .paths()
                    .first()
                    .unwrap()
                    .to_string_lossy()
                    .to_string(),
                config,
            )
        })
        .collect::<HashMap<String, FIMConfig>>();

        let mut should_restart = false;
        for (disk_path, rd) in removeable_disk.iter() {
            if fim_configs.contains_key(disk_path) == false {
                // insert new disk here
                let fim_config = FIMConfig {
                    id: PrimaryKey::LocalId(Uuid::new_v4().to_string()),
                    category: "USB".to_owned(),
                    config_type: FIMConfigType::FIM,
                    is_usb: Some(true),
                    exclude_path: vec![],
                    include_path: vec![rd.mount_point().to_path_buf()],
                };
                match fim_config.persist().await {
                    Ok(_) => {
                        should_restart = true;
                    }
                    _ => {}
                }
            }
        }

        for (disk_path, config) in fim_configs {
            if removeable_disk.contains_key(&disk_path) == false {
                // remove fim config
                match config.delete().await {
                    Ok(_) => {
                        should_restart = true;
                    }
                    _ => {}
                }
            }
        }

        should_restart
    }

    pub fn spawn_usb_watching_task(&self) -> JoinHandle<()> {
        let mut disks = Disks::new_with_refreshed_list();
        let watch_restart_tx = self.restart_watch_agent_tx.clone();
        let logger = self.logger();
        let subscriber = logger.subscriber();
        let mut shutdown_signal = get_shutdown_signal();

        tokio::task::Builder::new()
            .name("FIM USB Watcher")
            .spawn(async move {
                let _guard = logger.guard();
                debug!("Starting usb watcher");
                loop {
                    select! {
                        biased;

                        _ = shutdown_signal.recv() => {
                            debug!("Shutting down usb watch task");
                            break;
                        },

                        _ = sleep(Duration::from_secs(5)) => {
                            disks.refresh(true);
                            if FIMAgent::sholud_restart_watcher_for_usb(&disks).await {
                                debug!("USB device changes has been detected restarting watch agent");
                                if let Err(error) = watch_restart_tx.send(true) {
                                    error!(?error, "Failed to send watch agent restart message");
                                }
                            }
                        }
                    }
                }
            }.with_subscriber(subscriber))
            .unwrap()
    }
}

#[async_trait]
impl AgentRunnable for FIMAgent<'static> {
    fn get_name(&self) -> &str {
        "fim_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting FIM Agent ------------------------");

        let mut data_sync_agent_manager = AgentManager::default();

        data_sync_agent_manager.add_agent(Agent::new(Box::new(FIMDataSyncAgent::new(
            self.agent_metadata,
        ))));

        let restart_rx = self.restart_watch_agent_tx.subscribe();

        let mut watch_agent_manager = AgentManager::default();
        watch_agent_manager.add_agent(Agent::new(Box::new(FIMWatchAgent::new(
            self.agent_metadata.get_endpoint_id(),
            restart_rx,
        ))));

        let mut usb_watcher_handle = self.spawn_usb_watching_task();

        let mut shutdown_signal = get_shutdown_signal();

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down FIM Agent");
                    break;
                },

                _ = data_sync_agent_manager.start() => {
                    info!("Data sync agent finished running");
                }

                _ = watch_agent_manager.start() => {
                    info!("Watch agent finished running");
                }

                _ = &mut usb_watcher_handle => {
                    info!("USB watcher task is finished running");
                }

            }
        }
        info!("---------------------- Stopped FIM Agent ------------------------");
        Ok(())
    }
}
