use crate::{has_commands::HasCommands, tasks::command_executor::CommandExecutor, TaskExecutable};
use anyhow::Error;
use database::models::FileAttachment;
use encoding_rs::UTF_16LE;
use encoding_rs_io::DecodeReaderBytesBuilder;
use logger::{debug, error};
use shell::ShellOutput;
use std::{io::Read, path::Path};
use tokio::{fs::OpenOptions, io::AsyncWriteExt};

pub struct Msi<'a> {
    resource: Box<dyn HasCommands>,
    attachment: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Msi<'a> {
    pub fn new(
        resource: Box<dyn HasCommands>,
        attachment: &'a FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            resource,
            attachment,
            task,
        }
    }

    pub fn install_command(&self) -> String {
        let ext = Path::new(&self.attachment.real_name)
            .extension()
            .unwrap()
            .to_str()
            .unwrap()
            .to_lowercase();

        let (command, arguments) = match ext.as_str() {
            "cab" => (
                "DISM.exe",
                format!(
                    "/Online /Add-Package /PackagePath:\"{}\" /LogPath:\"{}\"",
                    self.attachment.path_to_file_str(),
                    "msi_log"
                ),
            ),
            "msu" => (
                "wusa.exe",
                format!("{} /quiet /norestart", self.attachment.real_name),
            ),
            "mst" => (
                "msiexec.exe",
                format!(
                    "TRANSFORMS={} /qn /norestart /L*! \"{}\"",
                    self.attachment.real_name, "msi_log"
                ),
            ),
            "msi" => (
                "msiexec.exe",
                format!(
                    "/i {} /qn /norestart /L*! \"{}\"",
                    self.attachment.real_name, "msi_log"
                ),
            ),
            "msp" => (
                "msiexec.exe",
                format!(
                    "/update {} /quiet /norestart /L*! \"{}\"",
                    self.attachment.real_name, "msi_log"
                ),
            ),
            "exe" => (self.attachment.real_name.as_str(), "/S".to_owned()),
            _ => {
                error!("Failed to build install command for extension {}", ext);
                ("", "".to_owned())
            }
        };

        format!("{} {}", command, arguments)
    }

    pub fn uninstall_command(&self) -> String {
        let ext = Path::new(&self.attachment.real_name)
            .extension()
            .unwrap()
            .to_str()
            .unwrap()
            .to_lowercase();

        let (command, arguments) = match ext.as_str() {
            "msu" => (
                "wusa.exe",
                format!("{} /quiet /norestart", self.attachment.real_name),
            ),
            "mst" => (
                "msiexec.exe",
                format!(
                    "/x TRANSFORMS=\"{}\" /qn /norestart /L* \"{}\"",
                    self.attachment.real_name, "msi_log"
                ),
            ),
            "msi" => (
                "msiexec.exe",
                format!(
                    "/x \"{}\" /qn /norestart /L* \"{}\"",
                    self.attachment.real_name, "msi_log"
                ),
            ),
            "msp" => (
                "msiexec.exe",
                format!(
                    "/x \"{}\" /quiet /norestart /L* \"{}\"",
                    self.attachment.real_name, "msi_log"
                ),
            ),
            _ => {
                error!("Failed to build uninstall command for extension {}", ext);
                ("", "".to_owned())
            }
        };

        format!("{} {}", command, arguments)
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        let command = self
            .resource
            .get_install_command()
            .map(|i| i.to_owned())
            .unwrap_or(self.install_command());

        let output = CommandExecutor::new_command(&command, self.task.clone())
            .capture()
            .execute()
            .await?;

        let msi_path = self.task.get_task_dir().join("msi_log");
        if command.contains("msi_log") && msi_path.exists() {
            if let Ok(msi_file) = std::fs::File::open(msi_path) {
                let mut reader = std::io::BufReader::new(
                    DecodeReaderBytesBuilder::new()
                        .encoding(Some(UTF_16LE))
                        .build(msi_file),
                );

                let mut msi_log_content = vec![];

                if let Ok(read_bytes) = reader.read_to_end(&mut msi_log_content) {
                    if read_bytes > 0 {
                        if let Ok(mut log_file) = OpenOptions::new()
                            .read(true)
                            .append(true)
                            .open(self.task.get_log_file())
                            .await
                        {
                            if log_file.write(&msi_log_content).await.is_ok() {
                                debug!("Merged MSI log to main output file");
                            };
                        } else {
                            error!("Failed to write msi log to main output file because of unable to open output log file");
                        }
                    } else {
                        error!("Failed to write msi log to main output file because msi file bytes are 0");
                    }
                } else {
                    error!("Failed to write msi log to main output file because of unable to read msi log file");
                }
            }
        }

        Ok(output)
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        let command = self
            .resource
            .get_uninstall_command()
            .map(|i| i.to_owned())
            .unwrap_or(self.uninstall_command());

        Ok(CommandExecutor::new_command(&command, self.task)
            .capture()
            .execute()
            .await?)
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        let command = self
            .resource
            .get_upgrade_command()
            .map(|i| i.to_owned())
            .unwrap_or(self.install_command());

        Ok(CommandExecutor::new_command(&command, self.task)
            .capture()
            .execute()
            .await?)
    }
}
