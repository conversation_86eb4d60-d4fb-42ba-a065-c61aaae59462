use crate::DEFAULT_MODULE_FILTER;

use super::Level;
use std::path::Path;
use tracing_subscriber::{fmt, EnvFilter};

#[derive(Debug, Default, Clone)]
pub struct LogOptions {
    module_name: String,
    path: String,
    file_name: String,
    filter: String,
    level: Level,
    suffix: String,
    use_utc: bool,
}

impl LogOptions {
    pub fn new<P: AsRef<Path>>(path: P, module_name: String, file_name: String) -> Self {
        Self {
            path: path.as_ref().to_str().unwrap().to_owned(),
            module_name,
            file_name,
            use_utc: true,
            ..Default::default()
        }
    }

    pub fn set_filter(&mut self, filter: String) -> &mut Self {
        self.filter = filter;
        self
    }

    pub fn set_suffix(&mut self, suffix: String) -> &mut Self {
        self.suffix = suffix;
        self
    }

    pub fn disable_utc(&mut self) -> &mut Self {
        self.use_utc = false;
        self
    }

    pub fn set_level(&mut self, level: Level) -> &mut Self {
        self.level = level;
        self
    }

    pub fn path(&self) -> &str {
        &self.path
    }

    pub fn file(&self) -> &str {
        &self.file_name
    }

    pub fn level(&self) -> &Level {
        &self.level
    }

    pub fn filter(&self, use_tokio_tracing: bool) -> EnvFilter {
        let mut filter: String = if self.filter.is_empty() {
            DEFAULT_MODULE_FILTER
                .replace("{LEVEL}", self.level.to_string().as_str())
                .replace("{MODULE}", self.module_name.as_str())
        } else {
            self.filter.clone()
        };
        if use_tokio_tracing {
            filter.push_str(",tokio=trace,runtime=trace");
        }
        filter.into()
    }

    pub fn suffix(&self) -> &str {
        if self.suffix.is_empty() {
            return "log";
        } else {
            &self.suffix
        }
    }

    pub fn timer<'a>() -> fmt::time::OffsetTime<Vec<time::format_description::FormatItem<'a>>> {
        let format = "[year]-[month padding:zero]-[day padding:zero] [hour]:[minute]:[second]";

        let timer: Vec<time::format_description::BorrowedFormatItem<'a>> =
            time::format_description::parse(format).unwrap();

        let offset_in_sec: i32 = chrono::Local::now().offset().local_minus_utc();
        let time_offset =
            time::UtcOffset::from_whole_seconds(offset_in_sec).unwrap_or(time::UtcOffset::UTC);

        let timer: fmt::time::OffsetTime<Vec<time::format_description::FormatItem>> =
            fmt::time::OffsetTime::new(time_offset, timer);

        timer
    }
}
