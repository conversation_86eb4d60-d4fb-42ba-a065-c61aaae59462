use super::os_service::OsService;
use crate::{DataCollectionError, DataCollectionExtension};
use anyhow::Result;
use async_trait::async_trait;
use database::models::AgentMetadata;
use logger::debug;
use serde_json::{json, Value};
use std::{collections::HashSet, time::Instant};

#[derive(Debug)]
pub struct OsServices<'a> {
    services: HashSet<OsService>,
    agent_metadata: &'a AgentMetadata,
}

impl<'a> OsServices<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> Self {
        Self {
            agent_metadata,
            services: HashSet::new(),
        }
    }
}

#[async_trait]
impl<'a> DataCollectionExtension for OsServices<'a> {
    fn get_refresh_interval(&self) -> u64 {
        self.agent_metadata
            .get_agent_refresh_settings()
            .service_refresh_cycle
    }

    fn get_name(&self) -> &str {
        "os_services_ext"
    }

    fn get_endpoint_id(&self) -> i64 {
        self.agent_metadata.get_endpoint_id()
    }

    fn build_payload(&self) -> Result<Value, DataCollectionError> {
        Ok(json!({
            "asset_id" : self.get_endpoint_id(),
            "data" : json!({
                    "service_details": self.services
                })
        }))
    }

    fn collect(&mut self) -> Result<(), DataCollectionError> {
        self.services = self.logger().with(|| {
            let time = Instant::now();
            let collection = OsService::collect();
            debug!("Time taken for collection {:?}", time.elapsed());
            collection
        });
        Ok(())
    }
}
