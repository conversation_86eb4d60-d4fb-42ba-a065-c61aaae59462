use agent_manager::Agent<PERSON><PERSON><PERSON><PERSON>;
use anyhow::Result;
use async_trait::async_trait;
use data_collection::DataCollectionExtension;
use logger::{info, ModuleLogger};
use std::time::Duration;
use tokio::{select, sync::RwLock, time::sleep};
use utils::{dir::get_log_dir, shutdown::get_shutdown_signal};

pub struct ExtensionAgent {
    name: String,
    extension: RwLock<Box<dyn DataCollectionExtension>>,
}

impl ExtensionAgent {
    pub fn new(extension: Box<dyn DataCollectionExtension>) -> Self {
        Self {
            name: extension.get_name().to_owned(),
            extension: RwLock::new(extension),
        }
    }
}

#[async_trait]
impl AgentRunnable for ExtensionAgent {
    fn logger(&self) -> ModuleLogger {
        ModuleLogger::new(
            self.get_name().to_lowercase().as_str(),
            None,
            Some(
                get_log_dir()
                    .join("extensions")
                    .join(self.get_name().to_lowercase())
                    .to_string_lossy()
                    .to_string(),
            ),
        )
    }

    fn get_name(&self) -> &str {
        &self.name
    }

    async fn start(&self) -> Result<()> {
        info!(
            "---------------------- Starting {} Extension ------------------------",
            self.get_name()
        );

        let mut extension = self.extension.write().await;

        extension.collect_and_send().await;

        drop(extension);

        let mut shutdown_signal = get_shutdown_signal();

        loop {
            let ext = self.extension.read().await;

            let interval = ext.get_refresh_interval();

            drop(ext);

            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down {} Extension", self.get_name());
                    break;
                }

                _ = sleep(Duration::from_secs(interval)) => {
                    let mut extension = self.extension.write().await;
                    extension.collect_and_send().await;
                },
            }
        }
        info!(
            "---------------------- Stopped {} Extension ------------------------",
            self.get_name()
        );
        Ok(())
    }
}
