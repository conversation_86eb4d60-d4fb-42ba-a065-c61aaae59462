use agent_manager::Agent<PERSON><PERSON><PERSON>ble;
use anyhow::Result;
use async_trait::async_trait;
use data_collection::DataCollectionExtension;
use logger::{error, info, ModuleLogger};
use std::{sync::Arc, time::Duration};
use tokio::{
    select,
    sync::{
        mpsc::{channel, Receiver, Sender},
        Mutex, RwLock,
    },
    time::sleep,
};
use utils::dir::get_log_dir;

pub struct ExtensionAgent {
    name: String,
    stop_signal_sender: Sender<bool>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
    extension: RwLock<Box<dyn DataCollectionExtension>>,
}

impl ExtensionAgent {
    pub fn new(extension: Box<dyn DataCollectionExtension>) -> Self {
        let (stop_signal_sender, stop_signal_receiver) = channel(1);
        Self {
            name: extension.get_name().to_owned(),
            extension: RwLock::new(extension),
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            stop_signal_sender,
        }
    }
}

#[async_trait]
impl AgentRunnable for ExtensionAgent {
    fn logger(&self) -> ModuleLogger {
        ModuleLogger::new(
            self.get_name().to_lowercase().as_str(),
            None,
            Some(
                get_log_dir()
                    .join("extensions")
                    .join(self.get_name().to_lowercase())
                    .to_string_lossy()
                    .to_string(),
            ),
        )
    }

    fn get_name(&self) -> &str {
        &self.name
    }

    async fn start(&self) -> Result<()> {
        info!(
            "---------------------- Starting {} Extension ------------------------",
            self.get_name()
        );

        let mut extension = self.extension.write().await;

        extension.collect_and_send().await;

        drop(extension);

        let mut receiver = self.stop_signal_receiver.lock().await;

        loop {
            let ext = self.extension.read().await;

            let interval = ext.get_refresh_interval();

            drop(ext);

            select! {
                biased;

                _ = receiver.recv() => {
                    info!("Shutting Down {} Extension", self.get_name());
                    break;
                }

                _ = sleep(Duration::from_secs(interval)) => {
                    let mut extension = self.extension.write().await;
                    extension.collect_and_send().await;
                },
            }
        }
        info!(
            "---------------------- Stopped {} Extension ------------------------",
            self.get_name()
        );
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if let Err(error) = self.stop_signal_sender.send(true).await {
            error!(
                ?error,
                "Failed to send stop signal to {} Extension",
                self.get_name()
            );
        }
        Ok(())
    }

    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
}
