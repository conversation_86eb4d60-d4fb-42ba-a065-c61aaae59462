use agent_manager::AgentRunnable;
use anyhow::Result;
use async_trait::async_trait;
use database::models::FIMConfig;
use database::{Model, PrimaryKey};
use fim::Watcher;
use futures::future::join_all;
use logger::{debug, error};
use logger::{info, WithSubscriber};
use std::sync::Arc;
use tokio::select;
use tokio::sync::broadcast::{self, Receiver, Sender};
use tokio::sync::Mutex;
use utils::shutdown::get_shutdown_signal;

pub struct FIMWatchAgent {
    endpoint_id: i64,
    restart_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl FIMWatchAgent {
    pub fn new(endpoint_id: i64, restart_receiver: Receiver<bool>) -> FIMWatchAgent {
        FIMWatchAgent {
            endpoint_id,
            restart_receiver: Arc::new(Mutex::new(restart_receiver)),
        }
    }

    fn build_generic_watchers(
        fim_configs: Vec<FIMConfig>,
        restart_tx: &Sender<bool>,
        endpoint_id: i64,
    ) -> Vec<Box<dyn Watcher>> {
        use database::models::FIMConfigType;
        use fim::generic_watcher::FIMWatcher;
        use fim::ConfigFileWatcher;

        let mut watchers: Vec<Box<dyn Watcher>> = vec![];

        for config in fim_configs {
            if config.config_type == FIMConfigType::FIM {
                watchers.push(Box::new(FIMWatcher::new(config, restart_tx.subscribe())));
            } else {
                watchers.push(Box::new(ConfigFileWatcher::new(
                    config,
                    endpoint_id,
                    restart_tx.subscribe(),
                )));
            }
        }

        watchers
    }
}

#[async_trait]
impl AgentRunnable for FIMWatchAgent {
    fn get_name(&self) -> &str {
        "fim_watch_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting FIM Watch Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();

        let mut restart_signal = self.restart_receiver.lock().await;

        let fim_configs = match FIMConfig::default().get_all(None).await {
            Ok(configs) => configs
                .into_iter()
                .map(|mut item| {
                    if item.is_usb.is_some_and(|i| i) {
                        item.id = PrimaryKey::default()
                    }
                    item
                })
                .collect::<Vec<FIMConfig>>(),
            Err(error) => {
                error!(?error, "Error reading FIM config from database");
                tokio::select! {
                    biased;

                    _ = shutdown_signal.recv() => {
                        info!("Shutting Down FIM Watch Agent");
                    },

                    _ = restart_signal.recv() => {
                        info!("Restarting FIM Watch Agent");
                    }
                };
                return Err(error.into());
            }
        };

        if fim_configs.len() == 0 {
            debug!("No configurations found so waiting");
            tokio::select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down FIM Watch Agent");
                },

                _ = restart_signal.recv() => {
                    info!("Restarting FIM Watch Agent");
                }
            };
            return Ok(());
        }

        let (restart_tx, _restart_signal) = broadcast::channel(16);

        let watchers = {
            #[cfg(windows)]
            {
                use utils::windows_driver_utility;

                if windows_driver_utility::has_driver() {
                    info!("Driver is available so using driver watcher");
                    vec![Box::new(fim::windows_driver_watcher::FIMWatcher::new(
                        fim_configs,
                        self.endpoint_id,
                        restart_tx.subscribe(),
                    )) as Box<dyn Watcher>]
                } else {
                    info!("Driver is not available so using generic watcher");
                    FIMWatchAgent::build_generic_watchers(
                        fim_configs,
                        &restart_tx,
                        self.endpoint_id,
                    )
                }
            }
            #[cfg(not(windows))]
            {
                FIMWatchAgent::build_generic_watchers(fim_configs, &restart_tx, self.endpoint_id)
            }
        };

        let mut watcher_task = tokio::task::Builder::new()
            .name("FIM Watcher Task")
            .spawn(
                async move {
                    join_all(watchers.iter().map(|item| item.watch())).await;
                }
                .with_subscriber(self.logger().subscriber()),
            )
            .unwrap();

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down FIM Watch Agent");
                    restart_tx.send(true).ok();
                    watcher_task.await.ok();
                    break;
                },

                _ = restart_signal.recv() => {
                    info!("Restarting FIM Watch Agent");
                    restart_tx.send(true).ok();
                    watcher_task.await.ok();
                    break;
                }

                _ = &mut watcher_task => {
                    info!("Finished all watchers execution");
                }
            }
        }

        info!("---------------------- Stopped FIM Watch Agent ------------------------");
        Ok(())
    }
}
