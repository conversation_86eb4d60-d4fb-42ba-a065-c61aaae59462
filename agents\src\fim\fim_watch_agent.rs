use std::sync::Arc;

use agent_manager::AgentRunnable;
use anyhow::Result;
use async_trait::async_trait;
use database::models::{FIMConfig, FIMConfigType};
use database::{Model, PrimaryKey};
use fim::{Confi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>atcher, Watcher};
use futures::future::join_all;
use logger::info;
use logger::{debug, error};
use tokio::select;
use tokio::sync::broadcast::{self, Receiver};
use tokio::sync::Mutex;
use utils::shutdown::get_shutdown_signal;

pub struct FIMWatchAgent {
    endpoint_id: i64,
    restart_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl FIMWatchAgent {
    pub fn new(endpoint_id: i64, restart_receiver: Receiver<bool>) -> FIMWatchAgent {
        FIMWatchAgent {
            endpoint_id,
            restart_receiver: Arc::new(Mutex::new(restart_receiver)),
        }
    }
}

#[async_trait]
impl AgentRunnable for FIMWatchAgent {
    fn get_name(&self) -> &str {
        "fim_watch_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting FIM Watch Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();

        let fim_configs = match FIMConfig::default().get_all(None).await {
            Ok(configs) => configs
                .into_iter()
                .map(|mut item| {
                    if item.is_usb.is_some_and(|i| i) {
                        item.id = PrimaryKey::default()
                    }
                    item
                })
                .collect::<Vec<FIMConfig>>(),
            Err(error) => {
                error!(?error, "Error reading FIM config from database");
                shutdown_signal.recv().await.ok();
                return Err(error.into());
            }
        };

        if fim_configs.len() == 0 {
            debug!("No configurations found so waiting");
            shutdown_signal.recv().await.ok();
            return Ok(());
        }

        let mut watchers: Vec<Box<dyn Watcher>> = Vec::new();

        let (restart_tx, _restart_signal) = broadcast::channel(16);

        for config in fim_configs {
            if config.config_type == FIMConfigType::FIM {
                watchers.push(Box::new(FIMWatcher::new(config, restart_tx.subscribe())));
            } else {
                watchers.push(Box::new(ConfigFileWatcher::new(
                    config,
                    self.endpoint_id,
                    restart_tx.subscribe(),
                )));
            }
        }

        let mut restart_signal = self.restart_receiver.lock().await;

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down FIM Watch Agent");
                    restart_tx.send(true).ok();
                    break;
                },

                _ = restart_signal.recv() => {
                    info!("Restarting FIM Watch Agent");
                    restart_tx.send(true).ok();
                    break;
                }

                _ = join_all(watchers.iter().map(|item| item.watch())) => {
                    info!("Finished all watchers execution");
                }
            }
        }
        info!("---------------------- Stopped FIM Watch Agent ------------------------");
        Ok(())
    }
}
