use agent_manager::Agent<PERSON>unnable;
use anyhow::Result;
use async_trait::async_trait;
use database::models::{FIMConfig, FIMConfigType};
use database::{Model, PrimaryKey};
use fim::{Config<PERSON><PERSON><PERSON>atch<PERSON>, <PERSON><PERSON><PERSON>atcher, Watcher};
use futures::future::join_all;
use logger::info;
use logger::{debug, error};
use std::sync::Arc;
use tokio::select;
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::sync::Mutex;

pub struct FIMWatchAgent {
    stop_signal_sender: Sender<bool>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
    endpoint_id: i64,
}

impl FIMWatchAgent {
    pub fn new(endpoint_id: i64) -> FIMWatchAgent {
        let (stop_signal_sender, stop_signal_receiver) = channel(1);

        FIMWatchAgent {
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            stop_signal_sender,
            endpoint_id,
        }
    }
}

#[async_trait]
impl AgentRunnable for FIMWatchAgent {
    fn get_name(&self) -> &str {
        "fim_watch_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting FIM Watch Agent ------------------------");
        let receiver_arc = self.stop_signal_receiver.clone();

        let mut receiver = receiver_arc.lock().await;

        let fim_configs = match FIMConfig::default().get_all(None).await {
            Ok(configs) => configs
                .into_iter()
                .map(|mut item| {
                    if item.is_usb.is_some_and(|i| i) {
                        item.id = PrimaryKey::default()
                    }
                    item
                })
                .collect::<Vec<FIMConfig>>(),
            Err(error) => {
                error!(?error, "Error reading FIM config from database");
                receiver.recv().await;
                return Err(error.into());
            }
        };

        if fim_configs.len() == 0 {
            debug!("No configurations found so waiting");
            receiver.recv().await;
            return Ok(());
        }

        let mut watchers: Vec<Box<dyn Watcher>> = Vec::new();

        for config in fim_configs {
            if config.config_type == FIMConfigType::FIM {
                watchers.push(Box::new(FIMWatcher::new(config)));
            } else {
                watchers.push(Box::new(ConfigFileWatcher::new(config, self.endpoint_id)));
            }
        }

        loop {
            select! {
                biased;

                _ = receiver.recv() => {
                    info!("Shutting Down FIM Watch Agent");
                    for w in watchers.into_iter() {
                        let _ = w.stop().await;
                    }
                    break;
                },

                _ = join_all(watchers.iter().map(|item| item.watch())) => {
                    info!("Finished all watchers execution");
                }
            }
        }
        info!("---------------------- Stopped FIM Watch Agent ------------------------");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if let Err(error) = self.stop_signal_sender.send(true).await {
            error!(?error, "Failed to send stop signal to fim watch agent");
        };
        Ok(())
    }

    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
}
