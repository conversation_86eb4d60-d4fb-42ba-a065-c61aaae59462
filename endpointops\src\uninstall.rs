use crate::{args::CmdArgs, prelude::EndpointopsError};
use console::Style;
use figlet_rs::FIGfont;
use logger::{debug, error, info, ModuleLogger};
use std::{fs, path::PathBuf, process::Command};
use utils::{
    constants::{
        ENDPOINTOPS_BINARY_NAME, ENDPOINTOPS_SERVICE_NAME, MESH_AGENT_BINARY_NAME,
        MANAGER_BINARY_NAME, MANAGER_SERVICE_NAME,
    },
    service_manager::ServiceManager,
};

fn install_and_stop_service(
    service_name: String,
    program: PathBuf,
    args: Vec<String>,
) -> Result<(), EndpointopsError> {
    let service = ServiceManager::new(service_name.clone(), program, None, args);

    // Get generic service by detecting what is available on the platform
    match service.stop() {
        Ok(()) => {
            info!("Service {} has been stopped successfully", service_name);
        }
        Err(error) => {
            error!(?error, "Failed to stop service {}", service_name);
        }
    };
    match service.uninstall() {
        Ok(()) => {
            info!("Service {} has been uninstalled successfully", service_name);
        }
        Err(error) => {
            error!(?error, "Failed to uninstall service {}", service_name);
        }
    };
    Ok(())
}

pub fn uninstall(args: &CmdArgs) -> Result<(), EndpointopsError> {
    let figfont = FIGfont::standard().expect("Unable to initilise fig");
    if let Some(figure) = figfont.convert("Uninstalling EndpointOps...") {
        let style = Style::new().blue();
        println!("{}", style.apply_to(figure));
    }

    let module_loger = ModuleLogger::new("uninstaller", None, Some("installer".to_owned()));

    let _guard = module_loger.guard();

    module_loger.set_global()?;

    info!("Logging installation");

    let install_dir = PathBuf::from(&args.install_path);

    if let Err(error) = install_and_stop_service(
        MANAGER_SERVICE_NAME.to_owned(),
        install_dir.join(MANAGER_BINARY_NAME),
        vec!["--manager".to_string()],
    ) {
        error!(
            ?error,
            "Error While uninstalling {} service", MANAGER_SERVICE_NAME
        );
    };

    if let Err(error) = install_and_stop_service(
        ENDPOINTOPS_SERVICE_NAME.to_owned(),
        install_dir.join(ENDPOINTOPS_BINARY_NAME),
        vec![
            "--agent".to_string(),
            format!("--install-path=\"{}\"", args.install_path),
        ],
    ) {
        error!(
            ?error,
            "Error While uninstalling {} service", ENDPOINTOPS_SERVICE_NAME
        );
    }

    let rdp_exe_path = install_dir.join(MESH_AGENT_BINARY_NAME);

    let rdp_uninstall = Command::new(rdp_exe_path.to_string_lossy().to_string())
        .arg("-uninstall")
        .output();

    match rdp_uninstall {
        Ok(result) => {
            debug!(
                "Output of rdp uninstallation {}",
                String::from_utf8_lossy(&result.stdout)
            );
        }
        Err(error) => {
            error!(?error, "Failed to execute -uninstall on rdp agent");
        }
    }

    post_uninstallation(args);

    match self_replace::self_delete_outside_path(&install_dir) {
        Err(error) => {
            error!(
                ?error,
                "Failed to delete binary out side of install directory"
            );
        }
        _ => {}
    };

    match fs::remove_dir_all(&install_dir) {
        Ok(_) => {
            debug!("Removed directory {}", install_dir.display());
        }
        Err(error) => {
            error!(?error, "Failed to remove directory")
        }
    };

    if let Some(figure) = figfont.convert("Uninstalled EndpointOps...") {
        let style = Style::new().blue();
        println!("{}", style.apply_to(figure));
    }

    Ok(())
}

fn post_uninstallation(_args: &CmdArgs) {
    #[cfg(windows)]
    {
        use winreg::enums::*;
        use winreg::RegKey;

        let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
        let path = r"Software\Microsoft\Windows\CurrentVersion\Uninstall";

        match hklm.open_subkey_with_flags(path, KEY_WRITE) {
            Ok(parent) => {
                match parent.delete_subkey_all("EndpointOps") {
                    Err(error) => {
                        error!(?error, "Failed to delete subkey of EndpointOps");
                    }
                    _ => {}
                };
            }
            Err(error) => {
                error!(?error, "Failed to open registry at path {}", path);
            }
        };
    }
}
