use crate::{has_commands::HasCommands, tasks::command_executor::CommandExecutor, TaskExecutable};
use anyhow::{anyhow, Error};
use database::models::FileAttachment;
use logger::{debug, error};
use shell::ShellOutput;
use std::{env::temp_dir, os::unix::fs::PermissionsExt};
use tokio::fs::{self, set_permissions};

pub struct Pkg<'a> {
    commands: Box<dyn HasCommands>,
    attachment: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Pkg<'a> {
    pub fn new(
        commands: Box<dyn HasCommands>,
        attachment: &'a FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            commands,
            attachment,
            task,
        }
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        let command = self
            .commands
            .get_install_command()
            .map(|i| i.to_owned())
            .unwrap_or(format!(
                "sudo installer -pkg '{}' -target /",
                self.attachment.path_to_file_str()
            ));

        Ok(CommandExecutor::new_command(&command, self.task)
            .execute()
            .await?)
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        let file_write_result =
            fs::write(temp_dir().join("pkguninstaller.sh"), PKG_UNINSTALL_SCRIPT).await;

        if let Err(error) = file_write_result {
            error!(
                ?error,
                "Failed to write pkg uninstall script to temp folder"
            );
            Err(anyhow!(
                "Failed to write pkg uninstall script to temp folder"
            ))
        } else {
            debug!("Wrote pkg uninstallation script");
            set_permissions(
                temp_dir().join("pkguninstaller.sh"),
                PermissionsExt::from_mode(0o777),
            )
            .await?;
            debug!("Made pkg uninstallation script as executable");

            let command = self
                .commands
                .get_uninstall_command()
                .map(|i| i.to_owned())
                .unwrap_or(format!(
                    "sh {} '{}'",
                    temp_dir().join("pkguninstaller.sh").display(),
                    self.attachment.real_name
                ));

            Ok(CommandExecutor::new_command(&command, self.task)
                .execute()
                .await?)
        }
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        let command = self
            .commands
            .get_upgrade_command()
            .map(|i| i.to_owned())
            .unwrap_or(format!(
                "sudo installer -pkg '{}' -target /",
                self.attachment.path_to_file_str()
            ));

        Ok(CommandExecutor::new_command(&command, self.task)
            .execute()
            .await?)
    }
}

const PKG_UNINSTALL_SCRIPT: &str = r#"
#!/bin/zsh
function uninstallpkg {
  xar -x -f "$1"
  if ! [ -f PackageInfo ]; then
    echo "Unable to find PackageInfo file in package"
    exit 1;
  fi
  pkgid=$(cat PackageInfo | tr -d '\r' | tr -d '\n' | sed 's:^.*identifier="\([^"]*\)".*$:\1:g')
  pkgutil --pkg-info "${pkgid}"
  pkgutil --only-files --files "${pkgid}" | tr '\n' '\0' | xargs -n 1 -0 sudo rm -i
  pkgutil --forget "${pkgid}"
}

uninstallpkg $1
"#;
