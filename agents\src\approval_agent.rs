use agent_manager::Agent<PERSON><PERSON><PERSON>ble;
use anyhow::Result;
use api::agent::request_enrollment;
use async_trait::async_trait;
use database::models::{EnrollmentStatus, ServerConfig};
use logger::{debug, error, info, trace};
use std::sync::Arc;
use std::time::Duration;
use tokio::select;
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::sync::Mutex;
use tokio::time::interval;

pub struct ApprovalAgent<'a> {
    server_config: &'a ServerConfig,
    stop_signal_sender: Sender<bool>,
    stop_signal_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl<'a> ApprovalAgent<'a> {
    pub fn new(server_config: &'a ServerConfig) -> ApprovalAgent<'a> {
        let (stop_signal_sender, stop_signal_receiver) = channel(1);

        ApprovalAgent {
            server_config,
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            stop_signal_sender,
        }
    }
}

#[async_trait]
impl AgentRunnable for ApprovalAgent<'static> {
    fn get_name(&self) -> &str {
        "approval_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting Approval Agent ------------------------");
        let mut shutdown_receiver = self.stop_signal_receiver.lock().await;

        let mut interval = interval(Duration::from_secs(10));
        loop {
            select! {
                biased;

                _ = shutdown_receiver.recv() => {
                    info!("Shutting Down Approval Agent");
                    break;
                }

                _ = interval.tick() => {
                    match request_enrollment(self.server_config).await {
                        Ok(result) => {
                            trace!("Got Agent approval status {:?}", result);
                            if result == EnrollmentStatus::Approved {
                                debug!("Got agent approval status as approved");
                                break;
                            }
                        }
                        Err(error) => {
                            error!(?error, "Failed to check enrollment request status");
                        }
                    };
                }


            }
        }
        info!("---------------------- Stopped Approval Agent ------------------------");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if let Err(error) = self.stop_signal_sender.send(true).await {
            error!(?error, "Failed to send stop signal to Approval agent");
        }
        Ok(())
    }

    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
}
