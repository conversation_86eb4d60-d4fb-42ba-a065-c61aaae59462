use agent_manager::Agent<PERSON>unna<PERSON>;
use anyhow::Result;
use api::data_collection::send_quick_check_data;
use async_trait::async_trait;
use database::models::{AgentMetadata, QuickCheck, Task, TaskStatus, TaskType};
use database::{<PERSON>, PrimaryKey, Uuid};
use logger::error;
use logger::info;
use serde::Serialize;
use serde_json::json;
use std::time::Duration;
use task_execution::{QuickCheckTask, TaskExecutable};
use tokio::select;
use tokio::time::sleep;
use utils::shutdown::get_shutdown_signal;

#[derive(Debug, Serialize, PartialEq, Eq, Hash)]
pub struct QuickCheckResult {
    id: i64,
    result: String,
    result_value: String,
}

pub struct QuickCheckAgent<'a> {
    agent_metadata: &'a AgentMetadata,
}

impl<'a> QuickCheckAgent<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> QuickCheckAgent<'a> {
        QuickCheckAgent { agent_metadata }
    }

    async fn process_quick_checks(&self) {
        let quick_checks = match QuickCheck::default().get_all(None).await {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to get quick checks from local database");
                vec![]
            }
        };

        // let mut results = vec![];
        let mut quick_check_results = vec![];
        for quick_check in quick_checks {
            let mut task = Task::default();
            task.id = PrimaryKey::LocalId(Uuid::new_v4().to_string());
            let id = quick_check.id.to_i64();
            task.quick_check = Some(quick_check);
            task.task_type = Some(TaskType::QuickCheck);
            let mut quick_check_task: Box<dyn TaskExecutable> = Box::new(QuickCheckTask::new(task));
            let result = quick_check_task.operate().await;
            quick_check_results.push(QuickCheckResult {
                id,
                result: if result.status == TaskStatus::Success {
                    "yes"
                } else {
                    "no"
                }
                .to_owned(),
                result_value: if result.status == TaskStatus::Success {
                    "".to_owned()
                } else {
                    result.output
                },
            });
        }

        if quick_check_results.len() > 0 {
            if let Err(error) = send_quick_check_data(json!({
                "asset_id" : self.agent_metadata.get_endpoint_id(),
                "data" : json!({
                    "quick_checks_result": quick_check_results,
                })
            }))
            .await
            {
                error!(
                    ?error,
                    "Failed to send quick check result {:?}", quick_check_results
                );
            }
        }
    }
}

#[async_trait]
impl AgentRunnable for QuickCheckAgent<'static> {
    fn get_name(&self) -> &str {
        "quick_check_agent"
    }

    async fn start(&self) -> Result<()> {
        info!("---------------------- Starting Quick Check Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();
        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down Quick Check Agent");
                    break;
                }

                _ = sleep(Duration::from_secs(
                    self.agent_metadata.get_agent_refresh_settings().quick_check_refresh_cycle,
                )) => {
                    self.process_quick_checks().await;
                },
            }
        }
        info!("---------------------- Stopped Quick Check Agent ------------------------");
        Ok(())
    }
}
